{"name": "@circularmarketplace/source", "version": "2.0.0", "license": "MIT", "scripts": {"build:all": "nx run-many --target=build --all --verbose", "build:webapp-marketplace": "nx build webapp-marketplace --verbose", "vercel-build": "nx build webapp-marketplace", "vercel:deploy": "npx vercel deploy", "build": "nx build webapp-marketplace", "start:dev": "nx run webapp-marketplace:dev", "create:ddb:table:auth:dev": "aws dynamodb create-table --cli-input-json file://packages/db/src/adapters/dynamodb/schema/auth-dev.json --region ap-southeast-2 --profile cm-dev", "create:ddb:table:account:dev": "aws dynamodb create-table --cli-input-json file://packages/db/src/adapters/dynamodb/schema/account-dev.json --region ap-southeast-2 --profile cm-dev", "create:ddb:table:account:prod": "aws dynamodb create-table --cli-input-json file://packages/db/src/adapters/dynamodb/schema/account-prod.json --region ap-southeast-2 --profile cm-dev", "create:ddb:table:auth:prod": "aws dynamodb create-table --cli-input-json file://packages/db/src/adapters/dynamodb/schema/auth-prod.json --region ap-southeast-2 --profile cm-dev"}, "private": true, "dependencies": {"@auth/dynamodb-adapter": "^2.7.4", "@aws-sdk/client-bedrock-runtime": "^3.835.0", "@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/client-rekognition": "^3.835.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@hookform/resolvers": "^4.1.2", "@next/env": "^15.2.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@sendgrid/mail": "^8.1.4", "@thi.ng/ksuid": "^3.2.81", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "electrodb": "^3.4.1", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.395.0", "moment": "^2.30.1", "next": "~15.1.4", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.4", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^1.7.4", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@nx-extend/shadcn-ui": "^4.2.1", "@nx/devkit": "20.4.6", "@nx/eslint": "20.4.6", "@nx/eslint-plugin": "20.4.6", "@nx/jest": "20.4.6", "@nx/js": "20.4.6", "@nx/next": "20.4.6", "@nx/playwright": "20.4.6", "@nx/react": "20.4.6", "@nx/storybook": "20.4.6", "@nx/web": "20.4.6", "@nx/workspace": "20.4.6", "@playwright/test": "^1.36.0", "@storybook/addon-essentials": "^8.4.6", "@storybook/addon-interactions": "^8.4.6", "@storybook/core-server": "^8.4.6", "@storybook/jest": "^0.2.3", "@storybook/nextjs": "^8.4.6", "@storybook/test-runner": "^0.19.0", "@storybook/testing-library": "^0.2.2", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "16.1.0", "@types/jest": "^29.5.14", "@types/node": "18.16.9", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "10.4.13", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "eslint-config-next": "14.2.16", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "nx": "20.4.6", "postcss": "8.4.38", "prettier": "^2.6.2", "storybook": "^8.4.6", "tailwindcss": "^3.4.6", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "verdaccio": "^5.0.4"}, "nx": {"includedScripts": []}}