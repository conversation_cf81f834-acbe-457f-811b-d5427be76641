import type { Profile } from '@package/db/core/models/account.model';

/**
 * Get user location from profile, prioritizing verification info over basic address
 * @param profile - User profile object
 * @returns Formatted location string (city, country) or fallback message
 */
export function getUserLocation(profile: Profile): string {
  // First priority: verification info address
  if (profile.verificationInfo?.address?.city && profile.verificationInfo?.address?.country) {
    return `${profile.verificationInfo.address.city}, ${profile.verificationInfo.address.country}`;
  }
  
  // Second priority: basic profile address
  if (profile.address?.city && profile.address?.country) {
    return `${profile.address.city}, ${profile.address.country}`;
  }
  
  // Fallback
  return 'Location not specified';
}

/**
 * Check if user has location information
 * @param profile - User profile object
 * @returns Boolean indicating if location is available
 */
export function hasUserLocation(profile: Profile): boolean {
  return !!(
    (profile.verificationInfo?.address?.city && profile.verificationInfo?.address?.country) ||
    (profile.address?.city && profile.address?.country)
  );
}
