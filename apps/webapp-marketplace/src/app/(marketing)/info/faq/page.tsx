import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertTriangle,
  CreditCard,
  HelpCircle,
  Info,
  ListPlus,
  Shield,
  User,
} from 'lucide-react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Frequently Asked Questions - Circular Marketplace',
  description:
    'Find answers to common questions about using our sustainable marketplace.',
};

// FAQ categories for better organization
const faqCategories = [
  {
    title: 'General',
    icon: <Info className="w-5 h-5 text-primary" />, // General info
    items: [
      {
        question: 'Where is Circular Marketplace built and operated from?',
        answer:
          'Circular Marketplace is a World first of its kind, proudly built, owned and operated in New Zealand. Circular Marketplace is currently available in New Zealand only.',
      },
      {
        question: 'How can I contact Circular Marketplace Support Centre?',
        answer:
          'Got a question or an issue? We are here to help - get in touch with us. Email <EMAIL>',
      },
      {
        question: 'How can I communicate with other users?',
        answer:
          'As an Owner, you can message a borrower once they request a rental booking on your "Rent It" listing or request the "Get it Free" listing from you. As an Owner, you can also message a Service Provider once they connect with you on your "Get it Fixed" listing. As a Borrower, you can message an Owner directly through the listing to get more information on the item before and after making the rental booking. You can also message the Owner to request their "Get it Free" listing. As a Service Provider, you can message an Owner on their "Fix It" listing to provide a quote/estimate.',
      },
      {
        question: 'What do I need to be aware of as an individual?',
        answer:
          'If you are an individual who is earning more than $60,000 annually on Circular Marketplace, you will need to have a GST number. Circular Marketplace is an intermediary between the Owner and the borrower so cannot offer any GST or tax advice. However, we can help you on how the platform works and operates. Circular Marketplace will generate a GST invoice for any Service Fees for each successful transaction. Circular Marketplace will always be free to join the marketplace for individuals, it is free to create listings for sharing, giving away and getting items fixed.',
      },
      {
        question: 'What do I need to be aware of as a Business?',
        answer:
          'If you are a business on Circular Marketplace, your rental prices and repair quotes need to include GST. Circular Marketplace does not cover insurance for listings from Businesses. Please check with your Insurance provider if additional cover is required to participate on a platform like Circular Marketplace. For all new businesses who sign up between 1st May 2024 to 31 January 2025, it is free for this duration. Monthly subscription cost of $20 inclusive of GST will be applied to your account from 1st of February 2025. We will notify you when this takes place, but until then enjoy and follow up on leads for free.',
      },
      {
        question:
          'Why should I trust Circular Marketplace and the users on this platform?',
        answer:
          'Trust starts with you. Circular Marketplace lets you share, giveaway and get your things fixed in a safe and transparent environment. We believe trust empowers you to live sustainably. Our platform encourages you to be part of an online community which embodies our core values of Honesty, Respect, Safety, Responsibility, Security and Being Genuine. Before any user can request a booking, they must go through a verification process, which verifies their Full Name, Date of Birth, Address and Mobile Number. We also partner with Stripe for secure payments. Each user has a profile which includes their name, profile picture, mobile number, description and feedback from other users. You will be in control of who and how you share your items at all times. The more you use Circular Marketplace, the more feedback and information there will be available on each user. If the Borrower is not verified, suggest they get verified first before accepting the booking. During the exchange of the item, be a friendly Kiwi and get to know the Borrower. As an Owner you have complete empowerment over who you choose to lend your item to!',
      },
      {
        question:
          'What are the safety tips for Owners dealing with new borrowers?',
        answer:
          'For Owners dealing with new borrowers without any feedback on Circular Marketplace, we recommend: 1) First and foremost, check whether the borrower is verified, 2) Ask the borrower to bring a valid ID to show before you hand over the item, 3) Request to take a photo of the Borrower with the item, 4) Meet at their house if possible.',
      },
      {
        question: 'How does Circular Marketplace work?',
        answer:
          'Circular Marketplace is a peer-to-peer marketplace that enables users to share, giveaway and repair things. For lending: 1) Create a listing for the item, 2) Approve incoming rental requests, 3) Arrange exchange time, 4) Get paid through our secure system. For renting: 1) Find items under "Rentals", 2) Book and pay securely, 3) Collect and enjoy, 4) Return and review. For giving away: 1) Create a listing, 2) Approve requests, 3) Arrange handover, 4) Leave review. For receiving free items: 1) Check "Free Listings", 2) Request and message, 3) Arrange handover, 4) Leave review. For repair listings: 1) Create a service listing, 2) Connect with providers, 3) Select provider, 4) Leave review. For service providers: 1) Select from "Fix Requests", 2) Connect with owner, 3) Provide quote, 4) Leave review.',
      },
    ],
  },
  {
    title: 'Account',
    icon: <User className="w-5 h-5 text-primary" />, // User/account
    items: [
      {
        question: 'How do I create an Account?',
        answer:
          "Follow these three simple steps:\n\n1. Click 'Sign up' in the top right-hand corner of the homepage. Sign up with your email address.\n2. Complete your profile by uploading a good-looking picture of yourself, add a 'Bio', this will help other Members to get to know you.\n3. Start browsing for items to rent and create a listing to lend.\n\nCongratulations — you're now officially a Member, welcome to the Circular Marketplace Family!\n\nTips:\n- If you want to list your items for lending, you need to provide your banking details on your dashboard, so you get paid for the things you are renting.\n- If you want to rent, then you need to get verified. You will be prompted to enter your full name, license number and type, address and phone number, and add the code you receive via text when you select an item to rent.",
      },
      {
        question: "How do I create a great 'Rent it out' listing?",
        answer:
          "Follow these key elements:\n\n- Item Name: Choose something that matches what potential borrowers might search for\n- Item Description: Be honest, clear and concise about specifications and usage\n- Category: Select the most relevant category\n- Location: Match your Profile Settings location\n- Time between rentals: Set duration for cleaning/service if needed\n- Pricing: Set daily rate and optional weekly discount\n- Delivery Options: Choose Pick Up/Drop Off or Delivery Available\n- Condition: Select Excellent (near new) or Average (over one year old with minor wear)\n- Condition Summary: Be honest about scratches, blemishes, and functionality\n- Safety Requirements: Mention any safety equipment needed\n- Photos: Take clear, honest photos showing the item's condition",
      },
      {
        question: 'What are the key ingredients for a popular listing?',
        answer:
          'To stand out and maximize your success:\n\n1. Capture great photos: Clear images lead to more rentals\n2. Be reasonable with pricing: 10-15% of retail price for daily rental, with attractive discounts for longer durations\n3. Provide honest descriptions: Clear specifications, compatibility, and condition details\n4. Be authentic: Upload your photo and add a bio to build trust\n5. Be responsive: Quick replies build rapport and lead to repeat business',
      },
      {
        question: "How do I create a great 'Giveaway' listing?",
        answer:
          'Follow these steps:\n\n- Item Name: Choose search-friendly names\n- Item Description: Be honest and clear about specifications\n- Category: Select the most relevant category\n- Location: Match your Dashboard Settings\n- Condition: Select Excellent (near new) or Average (over one year old)\n- Photos: Take clear, honest photos',
      },
      {
        question: "How do I create a great 'Listing' listing?",
        answer:
          'Follow these steps:\n\n- Item Name: Choose search-friendly names\n- Description: Include brand, make/model, age, and condition\n- Category: Select the most relevant category\n- Location: Match your Profile Settings\n- Photos: Take clear photos to show the item accurately',
      },
    ],
  },
  {
    title: 'Conflicts',
    icon: <AlertTriangle className="w-5 h-5 text-primary" />, // Alert/conflict
    items: [
      {
        question:
          'How can I prove that the item has been damaged during the rental?',
        answer:
          'The Owner and the borrower are encouraged to capture evidence of the condition of the item 24 hours prior to and post rental. This will form an evidenced based approach if something does go wrong.\n\nFor Owners: You need to take a timestamped photo of the item no more than 24 hours before the start of the rental.\n\nFor Borrowers: You need to take a timestamped photo during the start of the rental and when you handover the item post rental.\n\nIf something goes wrong, we encourage you to first settle between the Owner and the borrower. This is the easiest and cheaper way for the borrower. If the item is damaged or needs replacement, and a consensus cannot be reached then <NAME_EMAIL> within 24 hours of the incident. Circular Marketplace will assign a Case Manager to gather timestamped pictures from the Owner and borrower and assess the evidence. The borrowers accept full liability for the item they rent on Circular Marketplace. If they fail to return the item as agreed, they are liable to pay for its full market value.',
      },
      {
        question: 'What happens if my item is returned late?',
        answer:
          'If a borrower is late in returning the item to the agreed time, to no fault of your own then they should compensate you directly to a mutually agreed amount. If this is not resolved between the Owner and the borrower, you can place feedback to reflect your experience.',
      },
    ],
  },
  {
    title: 'Payments',
    icon: <CreditCard className="w-5 h-5 text-primary" />, // Payments
    items: [
      {
        question: 'How much does Circular Marketplace charge for rentals?',
        answer:
          'For the near future, we are offering Circular Marketplace as a free platform for our Members, we are not charging a service fee on the rental transaction. Stripe (payment Gateway) will charge the owner a minor fee for the transaction. This means that if an item is rented out for $200/week, the borrower pays $200 and the Owner earns $200 less Stripe Fees (2.9% + 0.30). The Stripe fee is automatically deducted from the Owners earnings, and the remaining balance is deposited in the nominated Bank Account. Circular Marketplace does not charge for signing up and creating listings for individuals. It will always be free to do this, we only charge a service fee when a successful transaction takes place. Circular Marketplace is free for registered businesses until 31 January 2025. Monthly subscription cost of $20 inclusive of GST will be applied to the account from 1st of February 2025.',
      },
      {
        question: 'What is the process of an Owner getting paid?',
        answer:
          'Before activating a listing on Circular Marketplace, you have to enter your Account Details in your personal dashboard so you receive payments when a rental transaction is successful. Once a rental request is accepted, the due amount will be in your account within 3 to 7 days. This is done through direct transfer, from our secure payment provider, Stripe. You are responsible to pay for any applicable GST for all rentals on Circular Marketplace. If you are a Business, it is your responsibility to provide a separate Invoice for the Borrower as per your tax obligations. Circular Marketplace will email an invoice for the Circular Marketplace Service Fee for the rental transaction.',
      },
      {
        question: 'What is the process of a Borrower getting charged?',
        answer:
          "Before requesting an item for rent, you will be prompted to get verified first. Once the verification process is completed, you can pay for the rental by credit or debit card only. The rental amount will be deducted from your account, to make sure you have enough funds for the rental. This will be deposited in the Owners account once the rental booking is accepted. If for any reason your rental doesn't go ahead, <NAME_EMAIL> with the Rental Identification Number and will release the full amount back into your account. This may take up to 48 business hours. Additional charges like delivery cost is paid in cash to the Owner.",
      },
    ],
  },
  {
    title: 'Promise',
    icon: <Shield className="w-5 h-5 text-primary" />, // Trust/safety
    items: [
      {
        question:
          'Why do I need to be verified in order to rent items on Circular Marketplace?',
        answer:
          "When we first launched Circular Marketplace, we promised a secure platform to earn, save and give. To enable this, all users, especially borrowers need to be verified - this is part of our Circular Marketplace Promise. The verification process involves authenticating the user's Full Name, Date of Birth, Address and Mobile Number. This process is triggered when a user makes a rental request. Once the user details are submitted, we start the authentication process through our third party electronic identification verification services (EIDV Services). The details of the metrics will not be visible publicly and will only be accessed to edit on your personal dashboard. Your first name will be visible publicly when a user accesses your profile.",
      },
      {
        question: 'Is insurance part of Circular Marketplace Promise?',
        answer:
          'We are unable to provide insurance on Circular Marketplace at this stage. We encourage you to get in touch with your Insurer and seek independent advice on insuring your items for rental on Circular Marketplace. Circular Marketplace will continue to evaluate our insurance options. This is definitely our intention, we will work hard on our end to identify a suitable solution from the insurance market in New Zealand.',
      },
      {
        question: 'Who is liable for the item during the rental period?',
        answer:
          "Borrowers are fully liable for loss or damages caused to the item during the rental period. Borrowers must therefore make sure they have the necessary funds available to replace or repair the item in the event that they're unable to return it in the condition it was rented in. Borrowers are fully responsible for a rented item in their possession during the rental period. In the unforeseen event of damage, theft, loss or confiscation, you're liable for the cost of rectifying the problem.",
      },
      {
        question: "What is the Owner's responsibility and accountability?",
        answer:
          "The Owner's responsibility is to be honest and truthful while listing their item for rental. The listing needs to be accurate in the description provided, fit for safe use and highlight any defects or blemishes upfront in the listing. Owners are ultimately accountable for renting out their items. Circular Marketplace is a service that connects Owners and borrowers. We do our best to enable users to rent safely, but it is your responsibility to protect your items and managing consequences if something goes wrong.",
      },
      {
        question:
          'What impact does Circular Marketplace have and how am I a part of this?',
        answer:
          'Circular Marketplace is a social enterprise, which means we have impact embedded within our purpose, operations and business model. On a global scale, we make an impact on the United Nations Sustainable Development Goals. The Sustainable Development Goals are a call for action by all countries - poor, rich and middle-income - to promote prosperity while protecting the planet. We directly or indirectly make an impact on the following Sustainable Development Goals: Goal 9: Industry Innovation and Infrastructure, Goal 11: Sustainable Cities and Communities, Goal 12: Responsible Consumption and Production, Goal 13: Climate Action, Goal 15: Life on Land, Goal 17: Partnerships for the Goals.',
      },
    ],
  },
  {
    title: 'Rentals',
    icon: <ListPlus className="w-5 h-5 text-primary" />, // Listings/rentals
    items: [
      {
        question: 'What is the process once the rental booking is placed?',
        answer:
          "For Borrowers: 1) Pending Verification: You need to be Verified to make a rental booking. 2) Awaiting Approval: You've been verified, and your request is pending approval from the Owner. You can now initiate contact through our messaging feature. 3) Not Accepted: Your request hasn't been accepted by the Owner. 4) Accepted: Your request has been accepted by the Owner. You can access the Owner's mobile number on their dashboard. Upon acceptance, both Owner and Borrower will receive an email highlighting the Rental Commencement and Due Date. Upon completion, both parties will receive an email reminding them to place feedback and <NAME_EMAIL> if something goes wrong. If something goes wrong during or at the end of the rental, email <EMAIL> as soon as possible or within 12 hours of the incident or the end of the rental agreement.",
      },
      {
        question: 'How to arrange the item handover and return?',
        answer:
          "The messaging feature on Circular Marketplace allows the Owner or Borrower to contact each other once the rental booking has been made. Once a request has been accepted, the Owner and the Borrower can request each other's contact number to arrange collection and drop-off. Choose a convenient place and time which both parties feel comfortable for handing over the item.",
      },
      {
        question: 'How are daily rentals calculated?',
        answer:
          'Daily rental rates are charged by a 24 hour period, meaning a rental from 2 August to 3 August will incur 1 day of rental charges, providing the item is returned less than 24 hours from the pick-up time. If it works for the Owner, the Borrower can extend the rental period, we suggest treating this as a new transaction and requesting the item for the new duration.',
      },
      {
        question: 'What happens when the item is returned late?',
        answer:
          'As a Borrower, you agree to return the item on time. For a one-day rental, this means if you picked it up at 2pm Monday, the default is that the item is due back on 2pm Tuesday - unless this time has been mutually agreed with each other with evidence. Owners are entitled to lodge a complaint to Circular Marketplace, and we will block the Borrower from using the platform in the future. The Owner can also place feedback for the Borrower with their negative experience.',
      },
      {
        question: 'How do I cancel my rental?',
        answer:
          'For Borrowers: 1) Before 24 hours Cancellation: 100% Refund (No Feedback Placed), 2) Within 24 hours Cancellation: 100% Refund (Owner has the ability to submit feedback). Multiple cancellations may result in being blocked from Circular Marketplace as a user. For Owners: 1) If you cancel before 24 hours of the rental start date, you will be charged Stripe Fees <NAME_EMAIL> and we will make a manual payment to cover the Stripe Fees (2.9% of the rental amount + $0.30), 2) If you cancel within 24 hours of the rental start date, you will be charged the Stripe Fees (2.9% of the rental amount + $0.30). If a user wants to cancel the transaction, they have <NAME_EMAIL> with the booking number so we can identify the transaction and action accordingly. If the Owner cancels the rental booking after confirmation, email <EMAIL> and we will help you find an alternative listing to rent or you can Browse Catalogue and find a suitable item to rent.',
      },
    ],
  },
  {
    title: 'Repairing',
    icon: <HelpCircle className="w-5 h-5 text-primary" />, // Help/repair
    items: [
      {
        question:
          'How does Circular Marketplace authenticate the Service Provider?',
        answer:
          "There are two scenarios for Service Provider authentication:\n\nFirst Scenario: If we first reach out to the Service Provider, we ensure we go through the Service Provider Onboarding Process prior to the Service Listing being created on Circular Marketplace.\n\nSecond Scenario: If the Service Provider proactively creates a Service Listing without Circular Marketplace's prior knowledge, we get daily notifications when a Service Listing is created and we take up to 48 hours from the creation of the Service Listing to go through the Service Provider Onboarding Process.\n\nIn both scenarios, we will physically contact to authenticate:\n- NZ Business Registration\n- Website\n- Contact Details\n- Social Media Channels\n\nImportant: If there are delays in completing the Onboarding Process, Circular Marketplace will take down the Service Listing until the process is completed. This only applies after the 48 hour period.\n\nCircular Marketplace does this so our users can trust and connect with quality Service Providers.",
      },
    ],
  },
];

export default function FAQPage() {
  return (
    <div className="container max-w-5xl px-4 py-8 mx-auto sm:py-10 md:py-12">
      <div className="mb-8 text-center sm:mb-10 md:mb-12">
        <h1 className="mb-3 text-2xl font-bold sm:text-3xl md:text-4xl">
          Frequently Asked Questions
        </h1>
        <div className="w-12 h-1 mx-auto mb-4 rounded-full sm:w-16 sm:mb-6 bg-primary/60" />
        <p className="text-base text-muted-foreground sm:text-lg">
          Find answers to the most common questions about using our marketplace
        </p>
      </div>

      <Tabs defaultValue={faqCategories[0].title} className="w-full">
        <div className="relative mb-6 overflow-x-auto pb-2 sm:mb-8 [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
          <TabsList className="flex gap-1 p-1 w-max sm:gap-2">
            {faqCategories.map((category) => (
              <TabsTrigger
                key={category.title}
                value={category.title}
                className="px-2.5 py-1.5 text-xs sm:text-sm sm:px-3 sm:py-2"
              >
                <span className="flex items-center gap-1 sm:gap-2">
                  {category.icon}
                  <span className="hidden xs:inline">{category.title}</span>
                </span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
        {faqCategories.map((category, categoryIndex) => (
          <TabsContent
            key={category.title}
            value={category.title}
            className="space-y-6 sm:space-y-8"
          >
            <div className="flex items-center gap-2 mb-4 sm:mb-6">
              <div className="p-2 rounded-full bg-primary/10">
                {category.icon}
              </div>
              <h2 className="text-lg font-semibold sm:text-xl">
                {category.title} Questions
              </h2>
            </div>
            <Accordion type="single" collapsible className="w-full">
              {category.items.map((faq, index) => (
                <AccordionItem
                  value={`${categoryIndex}-item-${index}`}
                  key={faq.question}
                  className="border border-border/60 rounded-md mb-3 px-2 data-[state=open]:bg-green-50/30"
                >
                  <AccordionTrigger className="py-3 pr-8 text-sm sm:text-base sm:py-4 hover:text-primary">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="pb-3 text-sm whitespace-pre-line sm:text-base sm:pb-4 text-muted-foreground">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </TabsContent>
        ))}
      </Tabs>

      <div className="p-4 mt-8 text-center border rounded-lg sm:p-6 sm:mt-12 md:mt-16 bg-green-50 border-primary/20">
        <h3 className="mb-2 text-base font-medium sm:text-lg">
          Still have questions?
        </h3>
        <p className="mb-4 text-sm text-muted-foreground sm:text-base">
          Our support team is here to help you with any other questions you
          might have.
        </p>
        <a
          href="mailto:<EMAIL>"
          className="inline-flex items-center px-3 py-1.5 text-sm text-white transition rounded-md sm:px-4 sm:py-2 sm:text-base bg-primary hover:bg-primary/90"
        >
          Contact Support
        </a>
      </div>
    </div>
  );
}
