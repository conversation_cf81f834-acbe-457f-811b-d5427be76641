'use client';

import { LogOut, Menu, Search, Settings, User } from 'lucide-react';
import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import type React from 'react';
import { Suspense, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Skeleton } from '@/components/ui/skeleton';

function SearchBar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const query = searchParams.get('query');
    if (query) {
      setSearchQuery(query);
    }
  }, [searchParams]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim().length >= 3) {
      router.push(`/marketplace/search?query=${searchQuery.trim()}`);
    }
  };

  return (
    <form onSubmit={handleSearch} className="relative w-full">
      <div className="relative w-full">
        <div className="absolute inset-y-0 flex items-center pointer-events-none left-3">
          <Search className="w-4 h-4 text-muted-foreground/60" />
        </div>
        <Input
          type="search"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search for keywords..."
          className="w-full pl-10 pr-4 hover:border-[hsl(150,67%,43%)] focus:border-[hsl(150,67%,43%)] focus:ring-1 focus:ring-[hsl(150,67%,43%)] transition-colors placeholder:text-muted-foreground/60"
        />
      </div>
    </form>
  );
}

function SearchBarFallback() {
  return <Skeleton className="w-full h-10" />;
}

function LogoMobile() {
  return (
    <Link href="/" className="flex items-center">
      <Image
        src="/images/logo-mobile.svg"
        alt="Circular Marketplace Logo"
        width={250}
        height={100}
        className="object-contain w-[150px] h-[60px] md:w-[200px] md:h-[80px] lg:w-[250px] lg:h-[100px]"
        priority
      />
    </Link>
  );
}

function LogoDesktop() {
  return (
    <Link href="/" className="flex items-center">
      <Image
        src="/images/logo-desktop.svg"
        alt="Circular Marketplace Logo"
        width={250}
        height={100}
        className="object-contain w-[150px] h-[60px] md:w-[200px] md:h-[80px] lg:w-[250px] lg:h-[100px]"
        priority
      />
    </Link>
  );
}

// Desktop Header Component (lg and above)
function DesktopHeader({ isLogin }: { isLogin: boolean }) {
  return (
    <div className="flex items-center justify-between py-3">
      <div className="shrink-0">
        <LogoDesktop />
      </div>

      <div className="flex-grow max-w-md mx-4 lg:max-w-full">
        <Suspense fallback={<SearchBarFallback />}>
          <SearchBar />
        </Suspense>
      </div>

      <nav className="flex items-center space-x-2">
        {isLogin ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="text-sm">
                My Account
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href="/dashboard/profile">
                  <User className="w-4 h-4 mr-2" />
                  Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/listings">
                  <Settings className="w-4 h-4 mr-2" />
                  My Listings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => signOut({ callbackUrl: '/' })}
                className="text-red-600"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Link href="/signin">
            <Button variant="ghost" size="sm" className="text-sm">
              Sign In
            </Button>
          </Link>
        )}
        <Link href="/dashboard/listings/create">
          <Button variant="default" size="sm" className="text-sm">
            Create Listing
          </Button>
        </Link>
      </nav>
    </div>
  );
}

// Main Header Component
export default function Header() {
  const { data: session, status } = useSession();
  const isLogin = status === 'authenticated' && !!session;
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="shadow-sm bg-background">
      <div className="container mx-auto">
        {/* Desktop View (lg and above) */}
        <div className="hidden lg:block">
          <DesktopHeader isLogin={isLogin} />
        </div>

        {/* Tablet and Mobile View (below lg) */}
        <div className="lg:hidden">
          <div className="flex items-center justify-between py-3">
            <div className="shrink-0">
              <LogoDesktop />
            </div>

            <div className="flex-grow hidden mx-4 md:block">
              <Suspense fallback={<SearchBarFallback />}>
                <SearchBar />
              </Suspense>
            </div>

            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" aria-label="Menu">
                  <Menu className="w-5 h-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[350px]">
                <div className="py-6 space-y-6">
                  <div className="flex items-center justify-between mb-5">
                    <span className="text-lg font-semibold">Menu</span>
                  </div>
                  <nav className="flex flex-col space-y-4">
                    {isLogin ? (
                      <>
                        <Link
                          href="/dashboard/profile"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Button
                            variant="ghost"
                            className="justify-start w-full"
                          >
                            <User className="w-4 h-4 mr-2" />
                            My Profile
                          </Button>
                        </Link>
                        <Link
                          href="/dashboard/listings"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Button
                            variant="ghost"
                            className="justify-start w-full"
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            My Listings
                          </Button>
                        </Link>
                        <Link
                          href="/dashboard/bookings"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <Button
                            variant="ghost"
                            className="justify-start w-full"
                          >
                            My Bookings
                          </Button>
                        </Link>
                        <Button
                          variant="ghost"
                          className="justify-start w-full text-red-600"
                          onClick={() => {
                            setIsMenuOpen(false);
                            signOut({ callbackUrl: '/' });
                          }}
                        >
                          <LogOut className="w-4 h-4 mr-2" />
                          Sign Out
                        </Button>
                      </>
                    ) : (
                      <Link href="/signin" onClick={() => setIsMenuOpen(false)}>
                        <Button
                          variant="ghost"
                          className="justify-start w-full"
                        >
                          Sign In
                        </Button>
                      </Link>
                    )}
                    <Link
                      href={isLogin ? '/dashboard/listings/create' : '/signin'}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Button variant="default" className="w-full">
                        Create Listing
                      </Button>
                    </Link>
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          </div>

          {/* Mobile Search Bar */}
          <div className="py-2 md:hidden">
            <Suspense fallback={<SearchBarFallback />}>
              <SearchBar />
            </Suspense>
          </div>
        </div>
      </div>
    </header>
  );
}
