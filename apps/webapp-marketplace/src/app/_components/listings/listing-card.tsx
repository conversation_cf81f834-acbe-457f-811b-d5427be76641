'use client';

import Image from 'next/image';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Listing, ListingType } from '@package/db/core/models';

interface ListingCardProps {
  item: Listing;
}

export function ListingCard({ item }: ListingCardProps) {
  const truncateText = (text: string, maxLength: number = 38) => {
    if (!text) return '';
    return text.length > maxLength
      ? `${text.substring(0, maxLength).trim()}...`
      : text;
  };

  return (
    <Link href={`/marketplace/item/${item.listingId}/details`}>
      <Card className="overflow-hidden transition-shadow hover:shadow-lg">
        <CardContent className="p-0">
          <div className="relative">
            <Badge
              className={`absolute top-2 right-2 z-10 px-3 py-1 font-medium text-sm shadow-md border-2 ${
                item.type === ListingType.FREE
                  ? 'bg-cm-teal text-white hover:bg-cm-teal-600 border-white/20'
                  : item.type === ListingType.RENT
                  ? 'bg-cm-salmon text-white hover:bg-cm-salmon-600 border-white/20'
                  : item.type === ListingType.SELL
                  ? 'bg-cm-lime text-cm-dark hover:bg-cm-lime-600 border-cm-dark/10'
                  : 'bg-purple-500 text-white hover:bg-purple-600 border-white/20' // Brighter purple for SWAP
              }`}
            >
              {item.type.toUpperCase()}
            </Badge>
            <Image
              alt={`Product ${item.name}`}
              className="object-cover w-full h-48"
              height={240}
              src={item.images.filter((image) => !!image.isMain)[0].url}
              width={400}
            />
          </div>
          <div className="flex flex-col h-full p-4">
            <h3 className="mb-2 text-lg font-semibold">
              {truncateText(item.name, 30)}
            </h3>
            <p className="flex-grow mb-2 text-sm text-muted-foreground">
              {truncateText(item.description, 38)}
            </p>
            <div className="pt-4 mt-auto border-t">
              <p className="text-lg font-bold">
                {item.type === ListingType.FREE
                  ? ListingType.FREE
                  : `$${item.price}`}{' '}
                {item.type === ListingType.RENT ? `/ ${item.rentalUnit}` : ''}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
