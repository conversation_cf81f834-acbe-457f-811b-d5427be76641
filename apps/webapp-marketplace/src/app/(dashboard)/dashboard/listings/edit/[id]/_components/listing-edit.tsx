'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState, useTransition } from 'react';

import {
  AlertCircle,
  Badge,
  CheckCircle,
  Loader2,
  Upload,
  X,
} from 'lucide-react';
import moment from 'moment';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import type { Listing } from '@package/db/core/models';
import {
  ListingCondition,
  ListingRentalUnit,
  ListingStatus,
  ListingType,
} from '@package/db/core/models';
import type { Category } from '@package/db/core/models/category.model';

import {
  getPresignedUrls,
  listCategories,
  updateListingById,
} from '../../../../_actions';

import ListingGuidelines from './listing-guidelines';

interface ListingEditProps {
  listing: Listing;
}

export default function ListingEdit({ listing }: ListingEditProps) {
  const router = useRouter();

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [isLoading, startLoadingTransition] = useTransition();
  const [isUpdatingListing, setUpdatingListingTransition] = useTransition();

  const [categories, setCategories] = useState<Category[]>([]);
  const [photos, setPhotos] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  const [listingType, setListingType] = useState<ListingType>(
    listing.type as ListingType
  );
  const [existingPhotos, setExistingPhotos] = useState<
    { url: string; isMain: boolean }[]
  >(listing.images || []);

  // Extract listing attributes for form fields
  const getAttributeValue = (key: string) => {
    if (!listing.attributes) return '';
    const attr = listing.attributes.find((attr) => attr.key === key);
    return attr ? attr.value : '';
  };

  const conditionSummary = getAttributeValue('conditionSummary');
  const safetyRequirements = getAttributeValue('safetyRequirements');
  const deliveryOptions = getAttributeValue('deliveryOptions').split(',');

  useEffect(() => {
    startLoadingTransition(async () => {
      const categoriesList = await listCategories();
      setCategories(categoriesList);
    });
  }, []);

  const [defaultStartDate, setDefaultStartDate] = useState(() => {
    if (listing.rentalAvailability && listing.rentalAvailability.length > 0) {
      return moment.utc(listing.rentalAvailability[0].startDate);
    }
    return moment(); // current local time
  });

  const [defaultEndDate, setDefaultEndDate] = useState(() => {
    if (listing.rentalAvailability && listing.rentalAvailability.length > 0) {
      return moment.utc(listing.rentalAvailability[0].endDate);
    }
    return moment().add(7, 'days');
  });

  const formatDateForDatetimeLocal = (momentDate: moment.Moment): string => {
    return momentDate.format('YYYY-MM-DDTHH:mm');
  };

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not supported. Please use JPG, PNG, GIF or WebP.`,
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File "${file.name}" is too large (${(
          file.size /
          1024 /
          1024
        ).toFixed(1)}MB). Maximum size is 10MB.`,
      };
    }

    return { valid: true };
  };

  const handlePhotoDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files);

    // Validate files
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of droppedFiles) {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else if (validation.error) {
        errors.push(validation.error);
      }
    }

    if (errors.length > 0) {
      setErrorMessage(`Some files were not added: ${errors.join('. ')}`);
    }

    setPhotos((prev) => [...prev, ...validFiles].slice(0, 10));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const uploadedFiles = Array.from(e.target.files);

      // Validate files
      const validFiles: File[] = [];
      const errors: string[] = [];

      for (const file of uploadedFiles) {
        const validation = validateFile(file);
        if (validation.valid) {
          validFiles.push(file);
        } else if (validation.error) {
          errors.push(validation.error);
        }
      }

      if (errors.length > 0) {
        setErrorMessage(`Some files were not added: ${errors.join('. ')}`);
      }

      setPhotos((prev) =>
        [...prev, ...validFiles].slice(0, 10 - existingPhotos.length)
      );
    }
  };

  const removePhoto = (index: number) => {
    setPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const removeExistingPhoto = (index: number) => {
    setExistingPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const advancedUploadPhotos = async (): Promise<string[]> => {
    if (photos.length === 0) return [];

    setIsUploading(true);
    const urls: string[] = [];

    try {
      // Get presigned URLs and upload to S3
      const fileTypes = photos.map((photo) => photo.type);
      const {
        urls: presignedUrls,
        bucket,
        region,
        error,
      } = await getPresignedUrls(fileTypes);

      if (error || !presignedUrls) {
        throw new Error(error || 'Failed to get upload URLs');
      }

      // Upload files directly to S3
      for (let i = 0; i < photos.length; i++) {
        const photo = photos[i];
        const { url: presignedUrl, key } = presignedUrls[i];

        const uploadResponse = await fetch(presignedUrl, {
          method: 'PUT',
          body: photo,
          headers: {
            'Content-Type': photo.type,
          },
        });

        if (!uploadResponse.ok) {
          throw new Error(
            `Failed to upload image ${i + 1}: ${uploadResponse.statusText}`
          );
        }

        const fileUrl = `https://${bucket}.s3.${region}.amazonaws.com/${key}`;

        urls.push(fileUrl);
        setUploadProgress(Math.round(((i + 1) / photos.length) * 100));
      }

      setUploadedUrls(urls);
      return urls;
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const getStatusBadgeColor = (status: ListingStatus) => {
    switch (status) {
      case ListingStatus.DRAFT:
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case ListingStatus.ACTIVE:
        return 'bg-green-100 text-green-800 border-green-200';
      case ListingStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case ListingStatus.SOLD:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDotColor = (status: ListingStatus) => {
    switch (status) {
      case ListingStatus.DRAFT:
        return 'bg-gray-500';
      case ListingStatus.ACTIVE:
        return 'bg-green-500';
      case ListingStatus.PENDING:
        return 'bg-yellow-500';
      case ListingStatus.SOLD:
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusDescription = (status: ListingStatus) => {
    switch (status) {
      case ListingStatus.DRAFT:
        return 'Listing is saved but not visible to others yet.';
      case ListingStatus.ACTIVE:
        return 'Listing is publicly visible and available for purchase/rent.';
      case ListingStatus.PENDING:
        return 'Listing is under review or awaiting approval.';
      case ListingStatus.SOLD:
        return 'Item has been sold or rented and is no longer available.';
      default:
        return '';
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage(null);
    setSuccessMessage(null);

    const form = e.currentTarget;

    setUpdatingListingTransition(async () => {
      try {
        // First upload any new photos
        let imageUrls: string[] = [];

        if (photos.length > 0) {
          try {
            imageUrls = await advancedUploadPhotos();
          } catch (error) {
            setErrorMessage('Failed to upload photos. Please try again.');
            return;
          }
        }

        const formData = new FormData(form);
        // Combine existing photos with newly uploaded photos
        const allImages = [
          ...existingPhotos.map((image, index) => ({
            url: image.url,
            isMain: index === 0 && imageUrls.length === 0, // First existing image is main only if no new uploads
          })),
          ...imageUrls.map((url, index) => ({
            url,
            isMain: existingPhotos.length === 0 && index === 0, // First new image is main only if no existing images
          })),
        ];

        if (allImages.length > 0) {
          // Make sure at least one image is marked as main
          if (!allImages.some((img) => img.isMain)) {
            allImages[0].isMain = true;
          }

          formData.set('imageUrls', JSON.stringify(allImages));
        }

        // Handle rental details for RENT listings
        if (formData.get('type') === ListingType.RENT) {
          const startDate = formData.get('startDate')?.toString();
          const endDate = formData.get('endDate')?.toString();

          if (startDate && endDate) {
            const rentalAvailability = [
              {
                startDate: moment.utc(startDate).format(),
                endDate: moment.utc(endDate).format(),
              },
            ];

            formData.set('rentalUnit', formData.get('rentalUnit') as string);
            formData.set(
              'rentalAvailability',
              JSON.stringify(rentalAvailability)
            );
          }
        }

        const result = await updateListingById(listing.listingId, formData);

        if (result.error) {
          setErrorMessage(result.error);
          return;
        }

        setSuccessMessage('Listing updated successfully');
      } catch (e) {
        setErrorMessage(
          'An error occurred while updating the listing. Please try again.'
        );
      }
    });
  };

  return (
    <div className="container p-6 mx-auto">
      <h1 className="mb-6 text-3xl font-bold">Edit Listing</h1>
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-6">
            {/* listing status */}
            <Card className="border-2 border-primary/30">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                  Listing Status
                  <Badge
                    className={`text-sm px-3 py-1 ${getStatusBadgeColor(
                      listing.status as ListingStatus
                    )}`}
                  >
                    {listing.status}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label htmlFor="status">Update Status</Label>
                  <Select name="status" defaultValue={listing.status} required>
                    <SelectTrigger id="status" className="font-medium">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ListingStatus)
                        .filter(
                          ([key]) => !['EXPIRED', 'DELETED'].includes(key)
                        )
                        .map(([key, value]) => (
                          <SelectItem key={key} value={value}>
                            <div className="flex items-center gap-2">
                              <div
                                className={`w-3 h-3 rounded-full ${getStatusDotColor(
                                  value
                                )}`}
                              />
                              {key.charAt(0).toUpperCase() +
                                key.slice(1).toLowerCase()}
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <p className="mt-1 text-xs text-muted-foreground">
                    {getStatusDescription(listing.status as ListingStatus)}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Item Details */}
            <Card>
              <CardHeader>
                <CardTitle>Item Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="listingType">Listing Type</Label>
                  <Select
                    name="type"
                    defaultValue={listing.type}
                    required
                    onValueChange={(value) =>
                      setListingType(value as ListingType)
                    }
                  >
                    <SelectTrigger id="listingType">
                      <SelectValue placeholder="Select listing type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ListingType).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {key.charAt(0).toUpperCase() +
                            key.slice(1).toLowerCase().replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    defaultValue={listing.name}
                    placeholder="Item name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    defaultValue={listing.description}
                    placeholder={
                      listingType === ListingType.SWAP
                        ? '`Eg: Swap Camping stove for hiking backpack'
                        : 'Describe your item'
                    }
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    name="categoryId"
                    defaultValue={listing.categoryId}
                    required
                  >
                    <SelectTrigger id="category">
                      <SelectValue
                        placeholder={
                          isLoading ? 'Loading categories' : 'Select category'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem
                          key={category.categoryId}
                          value={category.categoryId}
                        >
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="price">Price</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    defaultValue={listing.price}
                    placeholder="Enter price"
                    required
                  />
                </div>
                <div>
                  <Label>Delivery Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pickup"
                        name="delivery_option_pickup"
                        defaultChecked={deliveryOptions.includes('PICKUP')}
                      />
                      <Label htmlFor="pickup">Local Pickup</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="shipping"
                        name="delivery_option_shipping"
                        defaultChecked={deliveryOptions.includes('SHIPPING')}
                      />
                      <Label htmlFor="shipping">Shipping</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="delivery"
                        name="delivery_option_delivery"
                        defaultChecked={deliveryOptions.includes('DELIVERY')}
                      />
                      <Label htmlFor="delivery">Local Delivery</Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Rental Details */}
            {listingType === ListingType.RENT && (
              <Card>
                <CardHeader>
                  <CardTitle>Rental Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="rentalUnit">Rental Unit</Label>
                    <Select
                      name="rentalUnit"
                      defaultValue={listing.rentalUnit || ListingRentalUnit.DAY}
                      required={listingType === ListingType.RENT}
                    >
                      <SelectTrigger id="rentalUnit">
                        <SelectValue placeholder="Select rental unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(ListingRentalUnit).map(
                          ([key, value]) => (
                            <SelectItem key={key} value={value}>
                              {key.charAt(0).toUpperCase() +
                                key.slice(1).toLowerCase()}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <Label>Rental Availability</Label>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Start Date</Label>
                        <Input
                          type="datetime-local"
                          id="startDate"
                          name="startDate"
                          min={formatDateForDatetimeLocal(moment())}
                          defaultValue={formatDateForDatetimeLocal(
                            defaultStartDate
                          )}
                          required={listingType === ListingType.RENT}
                          onChange={(e) => {
                            const newStartDate = moment(e.target.value);
                            if (newStartDate.isValid()) {
                              if (newStartDate.isSameOrAfter(defaultEndDate)) {
                                const newEndDate = moment(newStartDate).add(
                                  7,
                                  'days'
                                );
                                setDefaultEndDate(newEndDate);
                                const endDateInput = document.getElementById(
                                  'endDate'
                                ) as HTMLInputElement;
                                if (endDateInput) {
                                  endDateInput.value =
                                    formatDateForDatetimeLocal(newEndDate);
                                }
                              }
                              setDefaultStartDate(newStartDate);
                            }
                          }}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="endDate">End Date</Label>
                        <Input
                          type="datetime-local"
                          id="endDate"
                          name="endDate"
                          min={formatDateForDatetimeLocal(defaultStartDate)}
                          defaultValue={formatDateForDatetimeLocal(
                            defaultEndDate
                          )}
                          required={listingType === ListingType.RENT}
                          onChange={(e) => {
                            const newEndDate = moment(e.target.value);
                            if (newEndDate.isValid()) {
                              setDefaultEndDate(newEndDate);
                            }
                          }}
                          className="w-full"
                        />
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      This defines when your item is available for rent. You can
                      add more availability periods later. Rental start date
                      must be at least tomorrow.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Item Condition */}
            <Card>
              <CardHeader>
                <CardTitle>Item Condition</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Condition</Label>
                  <RadioGroup name="condition" defaultValue={listing.condition}>
                    {Object.entries(ListingCondition).map(([key, value]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <RadioGroupItem value={value} id={`condition-${key}`} />
                        <Label htmlFor={`condition-${key}`}>
                          {key.charAt(0).toUpperCase() +
                            key.slice(1).toLowerCase().replace('_', ' ')}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
                <div>
                  <Label htmlFor="conditionSummary">Condition Summary</Label>
                  <Textarea
                    id="conditionSummary"
                    name="conditionSummary"
                    defaultValue={conditionSummary}
                    placeholder="Describe the condition"
                  />
                </div>
                <div>
                  <Label htmlFor="safetyRequirements">
                    Safety Requirements
                  </Label>
                  <Textarea
                    id="safetyRequirements"
                    name="safetyRequirements"
                    defaultValue={safetyRequirements}
                    placeholder="List any safety requirements"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Photos */}
            <Card>
              <CardHeader>
                <CardTitle>Photos</CardTitle>
                <CardDescription>Update your listing photos</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Drag & drop upload area */}
                <div
                  className="p-6 mb-4 text-center border-2 border-gray-300 border-dashed rounded-lg cursor-pointer"
                  onDragOver={(e) => e.preventDefault()}
                  onDrop={handlePhotoDrop}
                  onClick={() =>
                    document.getElementById('photo-upload')?.click()
                  }
                  onKeyUp={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      document.getElementById('photo-upload')?.click();
                    }
                  }}
                >
                  <Upload className="w-12 h-12 mx-auto text-gray-400" />
                  <p className="mt-1">
                    Drag and drop your photos here, or click to select files
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={handlePhotoUpload}
                    id="photo-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="mt-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      document.getElementById('photo-upload')?.click();
                    }}
                  >
                    Select Files
                  </Button>
                </div>

                {/* Upload progress */}
                {isUploading && (
                  <div className="mb-4">
                    <p className="mb-2">Uploading photos: {uploadProgress}%</p>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-primary h-2.5 rounded-full"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Existing photos */}
                {existingPhotos.length > 0 && (
                  <>
                    <h4 className="mb-2 font-medium">Current Photos</h4>
                    <div className="grid grid-cols-2 gap-2 mb-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                      {existingPhotos.map((image, index) => (
                        <div key={image.url} className="relative">
                          <img
                            src={image.url}
                            alt={`Item view ${index + 1}`}
                            className="object-cover w-full h-20 rounded"
                          />
                          <button
                            type="button"
                            onClick={() => removeExistingPhoto(index)}
                            className="absolute top-0 right-0 p-1 text-white bg-red-500 rounded-full"
                          >
                            <X className="w-4 h-4" />
                          </button>
                          {image.isMain && (
                            <div className="absolute bottom-0 left-0 px-2 py-1 text-xs text-white rounded-tr-lg rounded-bl-lg bg-primary">
                              Main
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </>
                )}

                {/* New photos preview */}
                {photos.length > 0 && (
                  <>
                    <h4 className="mb-2 font-medium">New Photos</h4>
                    <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                      {photos.map((photo, index) => (
                        <div
                          key={`${photo.name}-${photo.lastModified}`}
                          className="relative"
                        >
                          <img
                            src={URL.createObjectURL(photo)}
                            alt={`Item preview ${index + 1}`}
                            className="object-cover w-full h-20 rounded"
                          />
                          <button
                            type="button"
                            onClick={() => removePhoto(index)}
                            className="absolute top-0 right-0 p-1 text-white bg-red-500 rounded-full"
                            disabled={isUploading}
                          >
                            <X className="w-4 h-4" />
                          </button>
                          {existingPhotos.length === 0 && index === 0 && (
                            <span className="absolute bottom-0 left-0 px-2 py-1 text-xs text-white rounded-tr-lg rounded-bl-lg bg-primary">
                              Main
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Success/Error Alerts and Buttons moved here for better mobile layout */}
            {errorMessage && (
              <Alert
                variant="destructive"
                className="mt-6 duration-300 border-red-500 bg-red-50 dark:bg-red-900/20 animate-in fade-in slide-in-from-top-5"
              >
                <AlertCircle className="w-5 h-5 text-red-500" />
                <AlertTitle className="font-semibold text-red-700 dark:text-red-300">
                  Error
                </AlertTitle>
                <AlertDescription className="text-red-600 dark:text-red-200">
                  {errorMessage}
                </AlertDescription>
              </Alert>
            )}
            {successMessage && (
              <Alert className="mt-6 duration-300 border-green-500 bg-green-50 dark:bg-green-900/20 animate-in fade-in slide-in-from-top-5">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <AlertTitle className="font-semibold text-green-700 dark:text-green-300">
                  Success
                </AlertTitle>
                <AlertDescription className="text-green-600 dark:text-green-200">
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}
            <div className="flex flex-wrap gap-3 mt-6 md:hidden">
              <Button
                type="submit"
                disabled={isUpdatingListing}
                className="flex-1"
              >
                {isUpdatingListing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update Listing'
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/dashboard/listings')}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>

          <div className="space-y-6">
            <ListingGuidelines />
          </div>
        </div>

        {/* Desktop-only buttons at the bottom */}
        <div className="hidden gap-3 mt-6 md:flex">
          <Button type="submit" disabled={isUpdatingListing}>
            {isUpdatingListing ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Listing'
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard/listings')}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}
