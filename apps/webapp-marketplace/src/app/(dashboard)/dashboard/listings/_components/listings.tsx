'use client';

import { useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';

import {
  AlertCircle,
  CheckCircle,
  Edit,
  Eye,
  Loader2,
  Plus,
  Tag,
  Trash2,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { Listing, ListingStatus } from '@package/db/core/models/listing.model';
import { deleteListingById } from '../../_actions';

// Status badge styling helper
function getStatusBadgeVariant(status: ListingStatus) {
  switch (status) {
    case ListingStatus.ACTIVE:
      return 'bg-green-100 text-green-800 hover:bg-green-200';
    case ListingStatus.DRAFT:
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    case ListingStatus.SOLD:
      return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  }
}

// Format currency helper
function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
}

// Format date helper
function formatDate(dateString?: string): string {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

interface ListingsProps {
  listings: Listing[];
  userId: string;
  searchParams: {
    page?: string;
    status?: string;
  };
}

export default function Listings({
  listings,
  userId,
  searchParams,
}: ListingsProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for delete confirmation dialog
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [listingToDelete, setListingToDelete] = useState<Listing | null>(null);

  // Filter and pagination logic (now client-side)
  const currentPage = Number(searchParams.page) || 1;
  const pageSize = 10;
  const statusFilter = searchParams.status || 'all';

  // Apply status filtering
  const filteredListings =
    statusFilter === 'all'
      ? listings
      : listings.filter((listing) => listing.status === statusFilter);

  // Apply pagination
  const totalItems = filteredListings.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const paginatedListings = filteredListings.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Handle status filtering
  const handleStatusChange = (value: string) => {
    const url = new URL(window.location.href);
    url.searchParams.set('status', value);
    url.searchParams.set('page', '1');
    router.push(url.toString());
  };

  const confirmDelete = (listing: Listing) => {
    setListingToDelete(listing);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!listingToDelete) return;

    setErrorMessage(null);
    setSuccessMessage(null);

    startTransition(async () => {
      try {
        const result = await deleteListingById(listingToDelete.listingId);

        if (result.error) {
          setErrorMessage(result.error);
        } else {
          setSuccessMessage(
            `"${listingToDelete.name}" has been deleted successfully.`
          );
          // Close the dialog
          setIsDeleteDialogOpen(false);
          // Refresh the page to update the list
          setTimeout(() => {
            router.refresh();
          }, 1500);
        }
      } catch (error) {
        console.error('Error deleting listing:', error);
        setErrorMessage('Failed to delete listing. Please try again.');
      }
    });
  };

  return (
    <div className="container px-4 py-4 mx-auto space-y-4 sm:px-6 sm:py-6 sm:space-y-6">
      {errorMessage && (
        <Alert
          variant="destructive"
          className="duration-300 border-red-500 animate-in fade-in slide-in-from-top-5 bg-red-50 dark:bg-red-900/20"
        >
          <AlertCircle className="w-4 h-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {successMessage && (
        <Alert className="duration-300 border-green-500 animate-in fade-in slide-in-from-top-5 bg-green-50 dark:bg-green-900/20">
          <CheckCircle className="w-4 h-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col items-center justify-between gap-3 sm:gap-4 sm:flex-row">
        <h1 className="text-xl font-bold sm:text-2xl md:text-3xl">
          My Listings
        </h1>
        <Button asChild size="sm" className="w-full text-sm sm:w-auto h-9">
          <Link href="/dashboard/listings/create">
            <Plus className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" /> Create
            Listing
          </Link>
        </Button>
      </div>

      <Card className="overflow-hidden">
        <CardHeader className="px-4 pt-4 pb-0 sm:p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle className="text-lg sm:text-xl">
                Manage Your Listings
              </CardTitle>
              <CardDescription className="text-sm">
                You have {totalItems}{' '}
                {totalItems === 1 ? 'listing' : 'listings'} in total
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select
                defaultValue={statusFilter}
                onValueChange={handleStatusChange}
              >
                <SelectTrigger className="w-full h-9 text-sm sm:w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={ListingStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={ListingStatus.DRAFT}>Draft</SelectItem>
                  <SelectItem value={ListingStatus.SOLD}>Sold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-4 pt-0 sm:p-6">
          {/* Rest of component remains the same */}
          {paginatedListings.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-6 text-center sm:py-8">
              <div className="p-2.5 sm:p-3 mb-3 sm:mb-4 rounded-full bg-primary/10">
                <Tag className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
              </div>
              <h3 className="text-base font-semibold sm:text-lg">
                No listings found
              </h3>
              <p className="max-w-xs mt-1 mb-4 text-xs sm:max-w-sm sm:text-sm text-muted-foreground">
                {statusFilter === 'all'
                  ? "You haven't created any listings yet."
                  : `You don't have any ${statusFilter.toLowerCase()} listings.`}
              </p>
              <Button asChild size="sm" className="text-sm h-9">
                <Link href="/dashboard/listings/create">
                  <Plus className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />{' '}
                  Create Your First Listing
                </Link>
              </Button>
            </div>
          ) : (
            <div className="-mx-6 overflow-x-auto sm:mx-0">
              {/* Table with enhanced mobile responsiveness */}
              <Table className="min-w-full border-collapse">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40%]">Listing</TableHead>
                    <TableHead className="w-[15%]">Price</TableHead>
                    <TableHead className="hidden sm:table-cell w-[15%]">
                      Status
                    </TableHead>
                    <TableHead className="hidden md:table-cell w-[15%]">
                      Created
                    </TableHead>
                    <TableHead className="text-right w-[15%]">
                      Actions
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedListings.map((listing) => (
                    <TableRow key={listing.listingId}>
                      <TableCell className="py-3 font-medium">
                        <div className="flex items-center gap-2 sm:gap-3">
                          <div className="relative flex-shrink-0 w-8 h-8 overflow-hidden rounded-md sm:w-10 sm:h-10 bg-muted">
                            {listing.images && listing.images[0] ? (
                              <Image
                                src={listing.images[0].url}
                                alt={listing.name}
                                width={40}
                                height={40}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="flex items-center justify-center w-full h-full bg-secondary/20">
                                <Tag className="w-3 h-3 opacity-50 sm:w-4 sm:h-4" />
                              </div>
                            )}
                            {/* Show mobile status badge overlaid on image for small screens */}
                            <div className="absolute top-0 right-0 sm:hidden">
                              <Badge
                                variant="outline"
                                className={`${getStatusBadgeVariant(
                                  listing.status
                                )} text-[8px] px-1 py-0`}
                              >
                                {listing.status}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex flex-col min-w-0">
                            <span className="truncate max-w-[90px] xs:max-w-[120px] sm:max-w-[180px] md:max-w-xs font-medium text-sm sm:text-base">
                              {listing.name}
                            </span>
                            {/* Show mobile date for small screens */}
                            <span className="text-[10px] sm:text-xs text-muted-foreground md:hidden">
                              {formatDate(listing.createdAt?.toString())}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-3 text-sm">
                        {formatPrice(listing.price)}
                      </TableCell>
                      <TableCell className="hidden py-3 sm:table-cell">
                        <Badge
                          variant="outline"
                          className={getStatusBadgeVariant(listing.status)}
                        >
                          {listing.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden py-3 text-sm md:table-cell">
                        {formatDate(listing.createdAt?.toString())}
                      </TableCell>
                      <TableCell className="py-3 text-right">
                        <div className="flex items-center justify-end gap-0.5 xs:gap-1 sm:gap-2">
                          <Button
                            size="icon"
                            variant="ghost"
                            asChild
                            className="hidden w-8 h-8 sm:inline-flex sm:w-auto sm:h-9 sm:px-3"
                          >
                            <Link
                              href={`/marketplace/item/${listing.listingId}/details`}
                              className="flex items-center"
                            >
                              <Eye className="w-4 h-4" />
                              <span className="sr-only sm:not-sr-only sm:ml-2">
                                View
                              </span>
                            </Link>
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            asChild
                            className="w-8 h-8 sm:w-auto sm:h-9 sm:px-3 sm:size-auto"
                          >
                            <Link
                              href={`/dashboard/listings/edit/${listing.listingId}`}
                              aria-label="Edit listing"
                              className="flex items-center"
                            >
                              <Edit className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                              <span className="sr-only sm:not-sr-only sm:ml-2">
                                Edit
                              </span>
                            </Link>
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="w-8 h-8 text-red-500 hover:text-red-600 sm:w-auto sm:h-9 sm:px-3 sm:size-auto"
                            onClick={() => confirmDelete(listing)}
                            disabled={isPending}
                            aria-label="Delete listing"
                          >
                            <Trash2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                            <span className="sr-only sm:not-sr-only sm:ml-2">
                              Delete
                            </span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination logic modified to use router.push instead of direct URL manipulation */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-4 sm:mt-6">
                  <Pagination>
                    <PaginationContent className="flex-wrap justify-center gap-1 sm:gap-0">
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationPrevious
                            href={`/dashboard/listings?page=${currentPage - 1}${
                              statusFilter !== 'all'
                                ? `&status=${statusFilter}`
                                : ''
                            }`}
                            className="h-8 text-xs sm:h-9 sm:text-sm"
                          />
                        </PaginationItem>
                      )}

                      {/* Mobile/tablet pagination - simplified */}
                      <div className="block sm:hidden">
                        <PaginationItem>
                          <span className="flex items-center justify-center h-8 text-xs">
                            Page {currentPage} of {totalPages}
                          </span>
                        </PaginationItem>
                      </div>

                      {/* Desktop pagination - show page numbers */}
                      <div className="hidden sm:flex">
                        {Array.from({ length: totalPages }).map((_, i) => {
                          const page = i + 1;
                          if (
                            page === 1 ||
                            page === totalPages ||
                            (page >= currentPage - 1 && page <= currentPage + 1)
                          ) {
                            return (
                              <PaginationItem key={page}>
                                <PaginationLink
                                  href={`/dashboard/listings?page=${page}${
                                    statusFilter !== 'all'
                                      ? `&status=${statusFilter}`
                                      : ''
                                  }`}
                                  isActive={page === currentPage}
                                  className="h-8 text-xs sm:h-9 sm:text-sm"
                                >
                                  {page}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          }

                          if (page === 2 || page === totalPages - 1) {
                            return (
                              <PaginationItem key={`ellipsis-${page}`}>
                                <PaginationEllipsis className="h-8 sm:h-9" />
                              </PaginationItem>
                            );
                          }

                          return null;
                        })}
                      </div>

                      {currentPage < totalPages && (
                        <PaginationItem>
                          <PaginationNext
                            href={`/dashboard/listings?page=${currentPage + 1}${
                              statusFilter !== 'all'
                                ? `&status=${statusFilter}`
                                : ''
                            }`}
                            className="h-8 text-xs sm:h-9 sm:text-sm"
                          />
                        </PaginationItem>
                      )}
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{listingToDelete?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {errorMessage && (
            <Alert variant="destructive">
              <AlertCircle className="w-4 h-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isPending}
              className="sm:order-1"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isPending}
              className="sm:order-2"
            >
              {isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Listing'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
