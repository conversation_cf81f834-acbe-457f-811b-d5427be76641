'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  CheckCircle,
  Eye,
  Loader2,
  Upload,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import type { Category } from '@package/db/core/models/category.model';

import type { ProcessedAIResult } from '@package/ai';

import {
  analyzeImageAction,
  testRekognitionConfigAction,
} from '../_actions/ai-analysis.action';

import { ListingCondition, ListingType } from '../../../_types/listing.types';

interface ListingFormData {
  type: ListingType;
  name: string;
  description: string;
  categoryId: string;
  price: number;
  condition: ListingCondition;
  conditionSummary?: string;
  safetyRequirements?: string;
}

interface AIAnalysisProps {
  useAI: boolean;
  setUseAI: (value: boolean) => void;
  aiResult: ProcessedAIResult | null;
  setAiResult: (result: ProcessedAIResult | null) => void;
  isAnalyzingImage: boolean;
  setIsAnalyzingImage: (value: boolean) => void;
  aiPhotoFile: File | null;
  setAiPhotoFile: (file: File | null) => void;
  form: UseFormReturn<ListingFormData>;
  categories: Category[];
  setPhotos: React.Dispatch<React.SetStateAction<File[]>>;
  setErrorMessage: (message: string | null) => void;
  validateFile: (file: File) => { valid: boolean; error?: string };
}

export default function AIAnalysis({
  useAI,
  setUseAI,
  aiResult,
  setAiResult,
  isAnalyzingImage,
  setIsAnalyzingImage,
  aiPhotoFile,
  setAiPhotoFile,
  form,
  categories,
  setPhotos,
  setErrorMessage,
  validateFile,
}: AIAnalysisProps) {
  // Configuration state
  const [configStatus, setConfigStatus] = useState<{
    useMockData: boolean;
    region: string;
    hasCredentials: boolean;
  } | null>(null);

  // Test configuration on component mount
  useEffect(() => {
    const testConfig = async () => {
      try {
        const result = await testRekognitionConfigAction();
        if (result.success) {
          setConfigStatus(result.config);
        }
      } catch (error) {
        console.error('Failed to test configuration:', error);
      }
    };
    testConfig();
  }, []);

  // AI photo upload and analysis handler
  const handleAIPhotoUpload = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];
    const validation = validateFile(file);

    if (!validation.valid) {
      setErrorMessage(validation.error || 'Invalid file');
      return;
    }

    // Clear previous results and form fields on new upload
    setAiResult(null);
    form.reset({
      type: form.getValues('type'), // Keep the listing type
      name: '',
      description: '',
      categoryId: '',
      price: 0,
      condition: ListingCondition.GOOD,
      conditionSummary: '',
      safetyRequirements: '',
    });

    setAiPhotoFile(file);
    setIsAnalyzingImage(true);
    setErrorMessage(null);

    try {
      // Create FormData for server action
      const formData = new FormData();
      formData.append('image', file);

      console.log('Starting AI analysis via server action...', {
        fileName: file.name,
        fileSize: file.size,
      });

      // Call server action
      const result = await analyzeImageAction(formData);

      if (!result.success) {
        setErrorMessage(result.error);
        return;
      }

      const processedResult = result.result;
      setAiResult(processedResult);

      console.log('AI analysis complete:', {
        itemName: processedResult.itemName,
        category: processedResult.category,
        hasContentWarning: !!processedResult.contentWarning,
      });

      // Don't auto-fill if content is flagged for moderation
      if (!processedResult.contentWarning) {
        // Auto-fill form fields only for clean content
        form.setValue('name', processedResult.itemName);
        form.setValue('description', processedResult.description);
        form.setValue(
          'condition',
          processedResult.condition as ListingCondition
        );

        // Find matching category (only if not zero analysis)
        if (processedResult.itemName !== 'No Data Detected') {
          const matchingCategory = categories.find(
            (cat) =>
              cat.name
                .toLowerCase()
                .includes(processedResult.category.toLowerCase()) ||
              processedResult.category
                .toLowerCase()
                .includes(cat.name.toLowerCase())
          );
          if (matchingCategory) {
            form.setValue('categoryId', matchingCategory.categoryId);
          }
        }

        // Add the photo to the photos array (only if content is clean)
        setPhotos((prev) => {
          const nonAIPhotos = prev.filter(
            (_, index) => index !== 0 || !aiPhotoFile
          );
          return [file, ...nonAIPhotos].slice(0, 10);
        });
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      setErrorMessage('Failed to analyze image. Please try again.');
    } finally {
      setIsAnalyzingImage(false);
    }
  };

  return (
    <>
      {/* AI Toggle */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI-Powered Listing
          </CardTitle>
          <CardDescription>
            Use AI to automatically fill listing details from a photo
          </CardDescription>
          <div className="p-3 mt-3 border rounded-lg bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
            <p className="text-xs text-amber-700 dark:text-amber-300">
              <strong>⚠️ AI Disclaimer:</strong> AI analysis is not perfect and
              may make mistakes. Always review and verify the auto-filled
              information before publishing your listing. Some images may not be
              recognized or may be flagged for content review.
            </p>
          </div>

          {/* Configuration Status */}
          {configStatus && (
            <div
              className={`p-3 mt-2 border rounded-lg ${
                configStatus.useMockData
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  : configStatus.hasCredentials
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}
            >
              <p
                className={`text-xs ${
                  configStatus.useMockData
                    ? 'text-blue-700 dark:text-blue-300'
                    : configStatus.hasCredentials
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}
              >
                <strong>
                  {configStatus.useMockData
                    ? '🧪 Mock Mode:'
                    : configStatus.hasCredentials
                    ? '☁️ AWS Rekognition:'
                    : '❌ Configuration:'}
                </strong>{' '}
                {configStatus.useMockData
                  ? 'Using simulated AI responses for development'
                  : configStatus.hasCredentials
                  ? `Connected to AWS Rekognition in ${configStatus.region}`
                  : 'AWS credentials not configured - using mock data'}
              </p>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="use-ai" checked={useAI} onCheckedChange={setUseAI} />
              <Label htmlFor="use-ai">Use AI to fill details from photo</Label>
            </div>

            {!useAI && (
              <div className="p-4 border rounded-lg bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900 dark:to-slate-900">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 rounded-full bg-gray-400 flex items-center justify-center mt-0.5">
                    <span className="text-xs font-bold text-white">!</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700 dark:text-gray-300">
                      Manual Entry Mode
                    </p>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                      Fill in all the listing details manually below. You can
                      enable AI anytime to auto-fill from a photo.
                    </p>
                    <div className="mt-2">
                      <button
                        type="button"
                        onClick={() => setUseAI(true)}
                        className="text-xs font-medium text-primary hover:text-primary/80"
                      >
                        Enable AI Analysis →
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* AI Photo Upload */}
      {useAI && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Upload Photo for AI Analysis
            </CardTitle>
            <CardDescription>
              Upload a photo and AI will analyze it to fill in the listing
              details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div
                className="p-6 text-center transition-colors border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-primary/50"
                onClick={() =>
                  document.getElementById('ai-photo-upload')?.click()
                }
              >
                {isAnalyzingImage ? (
                  <div className="flex flex-col items-center space-y-3">
                    <Loader2 className="w-12 h-12 mx-auto text-primary animate-spin" />
                    <div className="text-center">
                      <p className="font-medium">Analyzing image with AI...</p>
                      <p className="mt-1 text-sm text-gray-500">
                        This may take a few seconds
                      </p>
                    </div>
                    <div className="w-full h-2 max-w-xs bg-gray-200 rounded-full">
                      <div
                        className="h-2 rounded-full bg-primary animate-pulse"
                        style={{ width: '60%' }}
                      ></div>
                    </div>
                  </div>
                ) : aiResult ? (
                  <div className="flex flex-col items-center space-y-2">
                    <CheckCircle className="w-12 h-12 mx-auto text-green-500" />
                    <p className="font-medium text-green-700">
                      Analysis Complete!
                    </p>
                    <p className="text-sm text-center text-gray-600">
                      Upload a new photo to analyze again
                    </p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center space-y-2">
                    <Brain className="w-12 h-12 mx-auto text-gray-400" />
                    <p className="font-medium">Upload Photo for AI Analysis</p>
                    <p className="text-sm text-center text-gray-500">
                      AI will automatically fill in listing details
                    </p>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAIPhotoUpload}
                  id="ai-photo-upload"
                  disabled={isAnalyzingImage}
                />
              </div>

              {/* AI Results Display */}
              {aiResult && (
                <div className="p-4 space-y-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                  <h4 className="flex items-center gap-2 font-semibold text-blue-900 dark:text-blue-100">
                    <Eye className="w-4 h-4" />
                    AI Analysis Results
                  </h4>

                  {aiResult.detectedLabels.length > 0 && (
                    <div>
                      <p className="mb-2 text-sm font-medium text-blue-800 dark:text-blue-200">
                        Detected Labels:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {aiResult.detectedLabels.map((label, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="text-xs"
                          >
                            {label}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {aiResult.detectedBrand && (
                    <div>
                      <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Detected Brand:
                      </p>
                      <Badge variant="outline">{aiResult.detectedBrand}</Badge>
                    </div>
                  )}

                  {aiResult.textDetections.length > 0 && (
                    <div>
                      <p className="mb-2 text-sm font-medium text-blue-800 dark:text-blue-200">
                        Text Found:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {aiResult.textDetections.map((text, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {text}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {aiResult.contentWarning && (
                    <Alert variant="destructive">
                      <AlertCircle className="w-4 h-4" />
                      <AlertTitle>🚫 Content Policy Violation</AlertTitle>
                      <AlertDescription>
                        {aiResult.contentWarning}
                        <div className="p-3 mt-3 bg-red-100 border border-red-200 rounded dark:bg-red-900/30 dark:border-red-800">
                          <p className="font-semibold text-red-800 dark:text-red-200">
                            ⛔ LISTING BLOCKED
                          </p>
                          <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                            This image violates our community guidelines and
                            cannot be used for creating a listing. Please upload
                            a different photo that complies with our policies to
                            continue.
                          </p>
                          <div className="mt-2">
                            <button
                              type="button"
                              onClick={() => {
                                setAiResult(null);
                                setAiPhotoFile(null);
                                document
                                  .getElementById('ai-photo-upload')
                                  ?.click();
                              }}
                              className="px-3 py-1 text-sm text-white bg-red-600 rounded hover:bg-red-700"
                            >
                              Upload Different Photo
                            </button>
                          </div>
                        </div>
                      </AlertDescription>
                    </Alert>
                  )}

                  {aiResult.faceWarning && (
                    <Alert>
                      <Eye className="w-4 h-4" />
                      <AlertTitle>Privacy Notice</AlertTitle>
                      <AlertDescription>
                        {aiResult.faceWarning}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}

              {/* Success message after AI analysis */}
              {aiResult && !aiResult.contentWarning && (
                <div
                  className={`p-4 border rounded-lg ${
                    aiResult.itemName === 'No Data Detected'
                      ? 'bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200 dark:border-red-800'
                      : aiResult.itemName === 'Unrecognized Item'
                      ? 'bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border-orange-200 dark:border-orange-800'
                      : 'bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {aiResult.itemName === 'No Data Detected' ? (
                      <X className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                    ) : aiResult.itemName === 'Unrecognized Item' ? (
                      <AlertCircle className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                    ) : (
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <p
                        className={`font-medium ${
                          aiResult.itemName === 'No Data Detected'
                            ? 'text-red-700 dark:text-red-300'
                            : aiResult.itemName === 'Unrecognized Item'
                            ? 'text-orange-700 dark:text-orange-300'
                            : 'text-green-700 dark:text-green-300'
                        }`}
                      >
                        {aiResult.itemName === 'No Data Detected'
                          ? 'No Data Detected in Image'
                          : aiResult.itemName === 'Unrecognized Item'
                          ? 'AI Could Not Identify Item'
                          : 'AI Analysis Complete!'}
                      </p>
                      <p
                        className={`mt-1 text-sm ${
                          aiResult.itemName === 'No Data Detected'
                            ? 'text-red-600 dark:text-red-400'
                            : aiResult.itemName === 'Unrecognized Item'
                            ? 'text-orange-600 dark:text-orange-400'
                            : 'text-green-600 dark:text-green-400'
                        }`}
                      >
                        {aiResult.itemName === 'No Data Detected'
                          ? 'The image appears to be empty, too dark, blurry, or unclear. Please upload a clearer photo or proceed with manual entry.'
                          : aiResult.itemName === 'Unrecognized Item'
                          ? 'The image quality may be poor or the item is unclear. Please manually fill in the details below for the best results.'
                          : 'Form fields have been auto-filled below. Review and edit them before publishing your listing.'}
                      </p>
                      {aiResult.itemName !== 'Unrecognized Item' &&
                        aiResult.itemName !== 'No Data Detected' && (
                          <div className="flex items-center mt-2 space-x-4 text-xs text-green-600 dark:text-green-400">
                            <span>✓ Item identified</span>
                            <span>✓ Description generated</span>
                            <span>✓ Category suggested</span>
                            <span>✓ Condition estimated</span>
                          </div>
                        )}
                    </div>
                  </div>
                </div>
              )}

              {/* Helpful tips for problematic images */}
              {aiResult &&
                (aiResult.contentWarning ||
                  aiResult.itemName === 'Unrecognized Item' ||
                  aiResult.itemName === 'No Data Detected') && (
                  <div className="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                    <h5 className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                      💡 Tips for Better AI Results
                    </h5>
                    <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                      <li>• Use well-lit, clear photos with good focus</li>
                      <li>• Ensure the item fills most of the frame</li>
                      <li>• Avoid cluttered backgrounds</li>
                      <li>• Take photos from multiple angles if needed</li>
                      <li>
                        • Ensure content complies with community guidelines
                      </li>
                    </ul>
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
