'use client';

import { UseFormReturn } from 'react-hook-form';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import type { Category } from '@package/db/core/models/category.model';

import type { ProcessedAIResult } from '@package/ai/aws/rekognition';

import { ListingCondition, ListingType } from '../../../_types/listing.types';

interface ListingFormData {
  type: ListingType;
  name: string;
  description: string;
  categoryId: string;
  price: number;
  condition: ListingCondition;
  conditionSummary?: string;
  safetyRequirements?: string;
}

interface ListingFormFieldsProps {
  form: UseFormReturn<ListingFormData>;
  categories: Category[];
  isLoading: boolean;
  listingType: ListingType | null;
  setListingType: (type: ListingType) => void;
  useAI: boolean;
  aiResult: ProcessedAIResult | null;
}

export default function ListingFormFields({
  form,
  categories,
  isLoading,
  listingType,
  setListingType,
  useAI,
  aiResult,
}: ListingFormFieldsProps) {
  return (
    <>
      {/* Item Details - Show only when AI is off OR AI analysis is complete */}
      {(!useAI || aiResult) && (
        <Card>
          <CardHeader>
            <CardTitle>Item Details</CardTitle>
            {useAI && aiResult && (
              <CardDescription>
                Fields have been auto-filled by AI. You can edit them below.
              </CardDescription>
            )}
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Listing Type</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      setListingType(value as ListingType);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select listing type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.entries(ListingType).map(([key, value]) => (
                        <SelectItem key={key} value={value}>
                          {key.charAt(0).toUpperCase() +
                            key.slice(1).toLowerCase().replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              rules={{
                required: 'Item name is required',
                minLength: {
                  value: 3,
                  message: 'Item name must be at least 3 characters',
                },
                maxLength: {
                  value: 100,
                  message: 'Item name must be less than 100 characters',
                },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Item name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={
                        listingType === ListingType.SWAP
                          ? 'Eg: Swap Camping stove for hiking backpack'
                          : 'Describe your item'
                      }
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            isLoading ? 'Loading categories' : 'Select category'
                          }
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem
                          key={category.categoryId}
                          value={category.categoryId}
                        >
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {listingType !== ListingType.FREE &&
              listingType !== ListingType.SWAP && (
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter price"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
          </CardContent>
        </Card>
      )}

      {/* Item Condition - Show only when AI is off OR AI analysis is complete */}
      {(!useAI || aiResult) && (
        <Card>
          <CardHeader>
            <CardTitle>Item Condition</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="condition"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Condition</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      {Object.entries(ListingCondition).map(([key, value]) => (
                        <FormItem
                          key={key}
                          className="flex items-center space-x-3 space-y-0"
                        >
                          <FormControl>
                            <RadioGroupItem value={value} />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {key.charAt(0).toUpperCase() +
                              key.slice(1).toLowerCase().replace('_', ' ')}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {(listingType === ListingType.SELL ||
              listingType === ListingType.RENT) && (
              <FormField
                control={form.control}
                name="conditionSummary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Condition Summary (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe any wear, damage, or notable features"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}
