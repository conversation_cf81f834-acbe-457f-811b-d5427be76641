'use server';

import {
  analyzeAndGenerateContent,
  getAIService,
  type AIGeneratedContent,
} from '@package/ai';

/**
 * Enhanced server action for AI image analysis with Bedrock integration
 */
export async function enhancedAnalyzeImageAction(
  formData: FormData
): Promise<
  | { success: true; result: AIGeneratedContent }
  | { success: false; error: string }
> {
  try {
    // Extract image file from form data
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    // Validate file type
    if (!imageFile.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image.',
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 10MB.' };
    }

    // Use the new AI service for complete workflow
    const { analysis, content } = await analyzeAndGenerateContent(imageFile);

    return { success: true, result: content };
  } catch (error) {
    console.error('Enhanced AI analysis error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'AI analysis failed',
    };
  }
}

/**
 * Server action to generate just an item name from image analysis
 */
export async function generateItemNameAction(
  formData: FormData
): Promise<
  { success: true; itemName: string } | { success: false; error: string }
> {
  try {
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    const aiService = getAIService();
    const { analysis } = await aiService.analyzeAndGenerate(imageFile);

    return { success: true, itemName: analysis.itemName };
  } catch (error) {
    console.error('Item name generation error:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Item name generation failed',
    };
  }
}

/**
 * Server action to refine an existing description using Bedrock
 */
export async function refineDescriptionAction(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string
): Promise<
  | { success: true; refinedDescription: string }
  | { success: false; error: string }
> {
  try {
    const aiService = getAIService();
    const refinedDescription = await aiService.refineDescription(
      initialDescription,
      itemName,
      category,
      condition
    );

    return { success: true, refinedDescription };
  } catch (error) {
    console.error('Description refinement error:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Description refinement failed',
    };
  }
}
