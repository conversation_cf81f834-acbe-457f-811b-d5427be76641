'use server';

import {
  analyzeImageWithRekognition,
  generateAIContent,
  generateItemName,
  generateInitialDescription,
  refineDescriptionWithBedrock,
  type RekognitionConfig,
  type BedrockConfig,
  type AIGeneratedContent,
} from '@package/ai';

/**
 * Enhanced server action for AI image analysis with Bedrock integration
 */
export async function enhancedAnalyzeImageAction(
  formData: FormData
): Promise<
  | { success: true; result: AIGeneratedContent }
  | { success: false; error: string }
> {
  try {
    // Extract image file from form data
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    // Validate file type
    if (!imageFile.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image.',
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 10MB.' };
    }

    // Get AWS Rekognition configuration
    const rekognitionConfig: RekognitionConfig = {
      useMockData: process.env.USE_MOCK_REKOGNITION === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
      confidenceThreshold: {
        labels: 80,
        text: 85,
        moderation: 75,
      },
    };

    // Get AWS Bedrock configuration
    const bedrockConfig: BedrockConfig = {
      useMockData: process.env.USE_MOCK_BEDROCK === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
      modelId: process.env.AWS_BEDROCK_MODEL_ID || 'anthropic.claude-3-haiku-20240307-v1:0',
      maxTokens: 1000,
      temperature: 0.7,
    };

    // Validate AWS credentials if not using mock data
    if (!rekognitionConfig.useMockData) {
      if (!rekognitionConfig.accessKeyId || !rekognitionConfig.secretAccessKey) {
        console.error('AWS credentials not configured');
        return {
          success: false,
          error: 'AWS services are not properly configured. Please contact support.',
        };
      }
    }

    console.log('Starting enhanced AI analysis...', {
      fileName: imageFile.name,
      fileSize: imageFile.size,
      useMockRekognition: rekognitionConfig.useMockData,
      useMockBedrock: bedrockConfig.useMockData,
      region: rekognitionConfig.region,
    });

    // Analyze image with AWS Rekognition
    const rekognitionResponse = await analyzeImageWithRekognition(
      imageFile,
      rekognitionConfig
    );

    if (!rekognitionResponse) {
      throw new Error('No response received from Rekognition service');
    }

    console.log('Rekognition analysis complete:', {
      labelsCount: rekognitionResponse.Labels.length,
      moderationLabelsCount: rekognitionResponse.ModerationLabels.length,
      textDetectionsCount: rekognitionResponse.TextDetections.length,
      faceDetailsCount: rekognitionResponse.FaceDetails.length,
    });

    // Generate AI-powered content using the new enhanced features
    const aiContent = await generateAIContent(rekognitionResponse, bedrockConfig);

    console.log('Enhanced AI content generation complete:', {
      itemName: aiContent.itemName,
      generationMethod: aiContent.generationMethod,
      confidence: aiContent.confidence,
      initialDescriptionLength: aiContent.initialDescription.length,
      refinedDescriptionLength: aiContent.refinedDescription.length,
    });

    return { success: true, result: aiContent };
  } catch (error) {
    console.error('Enhanced AI analysis error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'AI analysis failed',
    };
  }
}

/**
 * Server action to generate just an item name from image analysis
 */
export async function generateItemNameAction(
  formData: FormData
): Promise<
  | { success: true; itemName: string }
  | { success: false; error: string }
> {
  try {
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    const rekognitionConfig: RekognitionConfig = {
      useMockData: process.env.USE_MOCK_REKOGNITION === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
    };

    const bedrockConfig: BedrockConfig = {
      useMockData: process.env.USE_MOCK_BEDROCK === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
    };

    // Analyze image
    const rekognitionResponse = await analyzeImageWithRekognition(
      imageFile,
      rekognitionConfig
    );

    // Generate item name
    const itemName = await generateItemName(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand,
      bedrockConfig
    );

    return { success: true, itemName };
  } catch (error) {
    console.error('Item name generation error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Item name generation failed',
    };
  }
}

/**
 * Server action to refine an existing description using Bedrock
 */
export async function refineDescriptionAction(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string
): Promise<
  | { success: true; refinedDescription: string }
  | { success: false; error: string }
> {
  try {
    const bedrockConfig: BedrockConfig = {
      useMockData: process.env.USE_MOCK_BEDROCK === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
    };

    const refinedDescription = await refineDescriptionWithBedrock(
      initialDescription,
      itemName,
      category,
      condition,
      bedrockConfig
    );

    return { success: true, refinedDescription };
  } catch (error) {
    console.error('Description refinement error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Description refinement failed',
    };
  }
}

/**
 * Test configuration for enhanced AI features
 */
export async function testEnhancedAIConfigAction(): Promise<{
  success: boolean;
  config: {
    useMockRekognition: boolean;
    useMockBedrock: boolean;
    region: string;
    hasCredentials: boolean;
    bedrockModelId: string;
  };
  error?: string;
}> {
  try {
    const config = {
      useMockRekognition: process.env.USE_MOCK_REKOGNITION === 'true',
      useMockBedrock: process.env.USE_MOCK_BEDROCK === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      hasCredentials: !!(
        process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
      ),
      bedrockModelId: process.env.AWS_BEDROCK_MODEL_ID || 'anthropic.claude-3-haiku-20240307-v1:0',
    };

    console.log('Enhanced AI configuration:', config);

    return { success: true, config };
  } catch (error) {
    console.error('Configuration test error:', error);
    return {
      success: false,
      config: {
        useMockRekognition: true,
        useMockBedrock: true,
        region: 'us-east-1',
        hasCredentials: false,
        bedrockModelId: 'anthropic.claude-3-haiku-20240307-v1:0',
      },
      error: error instanceof Error ? error.message : 'Configuration test failed',
    };
  }
}
