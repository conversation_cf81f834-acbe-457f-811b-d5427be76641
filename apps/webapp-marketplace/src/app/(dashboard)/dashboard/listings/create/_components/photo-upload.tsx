'use client';

import { Loader2, Upload, X } from 'lucide-react';
import { useRef } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import type { ProcessedAIResult } from '@package/ai';

interface PhotoUploadProps {
  photos: File[];
  setPhotos: React.Dispatch<React.SetStateAction<File[]>>;
  isUploading: boolean;
  uploadProgress: number;
  useAI: boolean;
  aiResult: ProcessedAIResult | null;
  validateFile: (file: File) => { valid: boolean; error?: string };
  setErrorMessage: (message: string | null) => void;
  isCreatingListing: boolean;
}

export default function PhotoUpload({
  photos,
  setPhotos,
  isUploading,
  uploadProgress,
  useAI,
  aiResult,
  validateFile,
  setErrorMessage,
  isCreatingListing,
}: PhotoUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(validation.error || 'Invalid file');
      }
    });

    if (errors.length > 0) {
      setErrorMessage(errors[0]);
      return;
    }

    setPhotos((prev) => [...prev, ...validFiles].slice(0, 10));
    setErrorMessage(null);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const removePhoto = (index: number) => {
    setPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <>
      {/* Upload Photos - Show only when AI is off OR when user wants to add more photos */}
      {(!useAI || aiResult) && (
        <Card>
          <CardHeader>
            <CardTitle>Upload Photos</CardTitle>
            <CardDescription>
              {useAI && aiResult
                ? 'Add more photos to your listing'
                : 'Drag and drop up to 10 photos'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div
                className="p-6 text-center transition-colors border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-primary/50"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="w-12 h-12 mx-auto text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB each
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => handleFileSelect(e.target.files)}
                  disabled={isUploading}
                />
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="w-full h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-2 transition-all duration-300 rounded-full bg-primary"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}

              {/* Photo Preview */}
              {photos.length > 0 && (
                <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                  {photos.map((photo, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Preview ${index + 1}`}
                        className="object-cover w-full h-24 border rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="absolute p-1 text-white transition-opacity bg-red-500 rounded-full opacity-0 -top-2 -right-2 group-hover:opacity-100"
                      >
                        <X className="w-4 h-4" />
                      </button>
                      {index === 0 && (
                        <div className="absolute px-2 py-1 text-xs text-white rounded bottom-1 left-1 bg-primary">
                          Main
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {photos.length >= 10 && (
                <p className="text-sm text-amber-600">
                  Maximum of 10 photos reached
                </p>
              )}

              {/* Submit Button */}
              <div className="mt-6">
                <Button type="submit" disabled={isCreatingListing}>
                  {isCreatingListing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />{' '}
                      Creating...
                    </>
                  ) : (
                    'Create Listing'
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
}
