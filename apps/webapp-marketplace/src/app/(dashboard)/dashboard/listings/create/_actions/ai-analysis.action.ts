'use server';

import {
  analyzeImageWithRekognition,
  processRekognitionResponse,
  type ProcessedAIResult,
  type RekognitionConfig,
} from '@package/ai';

/**
 * Server action for AI image analysis using AWS Rekognition
 */
export async function analyzeImageAction(
  formData: FormData
): Promise<
  | { success: true; result: ProcessedAIResult }
  | { success: false; error: string }
> {
  try {
    // Extract image file from form data
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    // Validate file type
    if (!imageFile.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image.',
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 10MB.' };
    }

    // Get AWS configuration from environment variables
    const config: RekognitionConfig = {
      useMockData: process.env.USE_MOCK_REKOGNITION === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      sessionToken: process.env.AWS_SESSION_TOKEN,
      confidenceThreshold: {
        labels: 80,
        text: 85,
        moderation: 75,
      },
    };

    // Validate AWS credentials if not using mock data
    if (!config.useMockData) {
      if (!config.accessKeyId || !config.secretAccessKey) {
        console.error('AWS credentials not configured');
        return {
          success: false,
          error:
            'AWS Rekognition is not properly configured. Please contact support.',
        };
      }
    }

    console.log('Starting AI analysis...', {
      fileName: imageFile.name,
      fileSize: imageFile.size,
      useMockData: config.useMockData,
      region: config.region,
    });

    // Analyze image with AWS Rekognition
    const rekognitionResponse = await analyzeImageWithRekognition(
      imageFile,
      config
    );

    if (!rekognitionResponse) {
      throw new Error('No response received from Rekognition service');
    }

    console.log('Rekognition analysis complete:', {
      labelsCount: rekognitionResponse.Labels.length,
      moderationLabelsCount: rekognitionResponse.ModerationLabels.length,
      textDetectionsCount: rekognitionResponse.TextDetections.length,
      faceDetailsCount: rekognitionResponse.FaceDetails.length,
    });

    // Process the response into structured result
    const processedResult = await processRekognitionResponse(
      rekognitionResponse
    );

    console.log('Processing complete:', {
      itemName: processedResult.itemName,
      category: processedResult.category,
      hasContentWarning: !!processedResult.contentWarning,
      hasFaceWarning: !!processedResult.faceWarning,
    });

    return { success: true, result: processedResult };
  } catch (error) {
    console.error('AI analysis error:', error);

    // Log additional error details for debugging
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }

    // Return user-friendly error message
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unexpected error occurred during image analysis';

    return {
      success: false,
      error: `Image analysis failed: ${errorMessage}`,
    };
  }
}

/**
 * Server action to test AWS Rekognition configuration
 */
export async function testRekognitionConfigAction(): Promise<{
  success: boolean;
  config: {
    useMockData: boolean;
    region: string;
    hasCredentials: boolean;
  };
  error?: string;
}> {
  try {
    const config = {
      useMockData: process.env.USE_MOCK_REKOGNITION === 'true',
      region: process.env.AWS_REGION || 'us-east-1',
      hasCredentials: !!(
        process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
      ),
    };

    console.log('Rekognition configuration:', config);

    return { success: true, config };
  } catch (error) {
    console.error('Configuration test error:', error);
    return {
      success: false,
      config: { useMockData: true, region: 'us-east-1', hasCredentials: false },
      error:
        error instanceof Error ? error.message : 'Configuration test failed',
    };
  }
}
