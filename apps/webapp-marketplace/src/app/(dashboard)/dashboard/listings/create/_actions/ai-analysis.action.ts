'use server';

import {
  getRekognitionService,
  type ProcessedAIResult,
  processRekognitionResponse,
} from '@package/ai';

/**
 * Server action for AI image analysis using AWS Rekognition
 */
export async function analyzeImageAction(
  formData: FormData
): Promise<
  | { success: true; result: ProcessedAIResult }
  | { success: false; error: string }
> {
  try {
    // Extract image file from form data
    const imageFile = formData.get('image') as File;

    if (!imageFile) {
      return { success: false, error: 'No image file provided' };
    }

    // TODO: move to common utils
    // Validate file type
    if (!imageFile.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image.',
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 10MB.' };
    }

    const rekognitionService = getRekognitionService();
    const rekognitionResponse = await rekognitionService.analyse(imageFile);

    // Process the response into structured result
    const processedResult = await processRekognitionResponse(
      rekognitionResponse
    );

    return { success: true, result: processedResult };
  } catch (error) {
    console.error('AI analysis error:', error);

    // Log additional error details for debugging
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
    }

    // Return user-friendly error message
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'An unexpected error occurred during image analysis';

    return {
      success: false,
      error: `Image analysis failed: ${errorMessage}`,
    };
  }
}
