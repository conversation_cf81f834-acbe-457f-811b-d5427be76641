'use client';

import { useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

import { ProfileOverview } from './profile-overview';
import { ProfileSettings } from './profile-settings';
import { ProfileReviews } from './profile-reviews';
import { ProfileVerification } from './profile-verification';

import type { Profile } from '@package/db/core/models/account.model';
import type { Feedback } from '@package/db/core/models/feedback.model';

interface ProfileTabsProps {
  profile: Profile;
  reviews: Feedback[];
}

export function ProfileTabs({ profile, reviews }: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="settings">Settings</TabsTrigger>
        <TabsTrigger value="reviews">Reviews ({reviews.length})</TabsTrigger>
        <TabsTrigger value="verification">Verification</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="mt-6">
        <ProfileOverview profile={profile} />
      </TabsContent>

      <TabsContent value="settings" className="mt-6">
        <ProfileSettings profile={profile} />
      </TabsContent>

      <TabsContent value="reviews" className="mt-6">
        <ProfileReviews reviews={reviews} />
      </TabsContent>

      <TabsContent value="verification" className="mt-6">
        <ProfileVerification profile={profile} />
      </TabsContent>
    </Tabs>
  );
}
