'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/hooks/use-toast';
import {
  Shield,
  Upload,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  CreditCard,
  FileImage,
} from 'lucide-react';

import { submitVerificationDocuments } from '../_actions/profile.action';

import type { Profile } from '@package/db/core/models/account.model';

interface ProfileVerificationProps {
  profile: Profile;
}

const documentTypes = [
  {
    value: 'drivers_license',
    label: "Driver's License",
    description: "Government-issued driver's license (front and back)",
    icon: CreditCard,
  },
  {
    value: 'passport',
    label: 'Passport',
    description: 'Valid passport (photo page)',
    icon: FileText,
  },
  {
    value: 'national_id',
    label: 'National ID Card',
    description: 'Government-issued national identification card',
    icon: CreditCard,
  },
  {
    value: 'utility_bill',
    label: 'Utility Bill',
    description: 'Recent utility bill for address verification',
    icon: FileText,
  },
];

export function ProfileVerification({ profile }: ProfileVerificationProps) {
  const router = useRouter();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [selectedDocumentType, setSelectedDocumentType] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
      ];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: 'Invalid file type',
          description: 'Please upload an image (JPG, PNG) or PDF file.',
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        toast({
          title: 'File too large',
          description: 'Please upload a file smaller than 10MB.',
          variant: 'destructive',
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleSubmitDocument = async () => {
    if (!selectedDocumentType || !selectedFile) {
      toast({
        title: 'Missing information',
        description: 'Please select a document type and upload a file.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('documentType', selectedDocumentType);
      formData.append('file', selectedFile);

      const result = await submitVerificationDocuments(formData);

      if (result.success) {
        toast({
          title: 'Document submitted',
          description:
            'Your verification document has been submitted for review.',
        });
        setSelectedDocumentType('');
        setSelectedFile(null);
        router.refresh();
      } else {
        toast({
          title: 'Submission failed',
          description: result.error || 'Failed to submit document',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Verification Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Verification Status
          </CardTitle>
          <CardDescription>
            Verify your identity to build trust and unlock additional features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-4 border rounded-lg">
              <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium">Identity Verification</h3>
                <p className="text-sm text-muted-foreground">
                  Not verified - Submit documents to get verified
                </p>
              </div>
              <div className="text-right">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Pending
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="text-center p-4 border rounded-lg">
                <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <h4 className="font-medium">Email Verified</h4>
                <p className="text-sm text-muted-foreground">
                  Your email is confirmed
                </p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <Clock className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                <h4 className="font-medium">Identity Pending</h4>
                <p className="text-sm text-muted-foreground">
                  Submit documents below
                </p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <h4 className="font-medium">Address Pending</h4>
                <p className="text-sm text-muted-foreground">
                  Verify your address
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Submit Verification Documents</CardTitle>
          <CardDescription>
            Upload official documents to verify your identity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Document Type Selection */}
          <div className="space-y-2">
            <Label>Document Type</Label>
            <Select
              value={selectedDocumentType}
              onValueChange={setSelectedDocumentType}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map((docType) => (
                  <SelectItem key={docType.value} value={docType.value}>
                    <div className="flex items-center gap-2">
                      <docType.icon className="w-4 h-4" />
                      {docType.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedDocumentType && (
              <p className="text-sm text-muted-foreground">
                {
                  documentTypes.find(
                    (doc) => doc.value === selectedDocumentType
                  )?.description
                }
              </p>
            )}
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label>Upload Document</Label>
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-primary/50 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              {selectedFile ? (
                <div className="space-y-2">
                  <FileImage className="w-8 h-8 text-green-500 mx-auto" />
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                  <p className="font-medium">Click to upload document</p>
                  <p className="text-sm text-muted-foreground">
                    JPG, PNG, or PDF up to 10MB
                  </p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleSubmitDocument}
            disabled={!selectedDocumentType || !selectedFile || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Submit Document
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Verification Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Benefits</CardTitle>
          <CardDescription>Why verify your account?</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-500" />
                Increased Trust
              </h4>
              <p className="text-sm text-muted-foreground">
                Verified users are more trusted by other marketplace members
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Higher Limits
              </h4>
              <p className="text-sm text-muted-foreground">
                Access to higher transaction limits and premium features
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <FileText className="w-4 h-4 text-green-500" />
                Better Visibility
              </h4>
              <p className="text-sm text-muted-foreground">
                Verified listings appear higher in search results
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <CreditCard className="w-4 h-4 text-green-500" />
                Payment Protection
              </h4>
              <p className="text-sm text-muted-foreground">
                Enhanced buyer and seller protection for transactions
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
