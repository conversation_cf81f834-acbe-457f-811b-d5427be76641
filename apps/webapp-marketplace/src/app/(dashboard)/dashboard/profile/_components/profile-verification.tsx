'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/components/ui/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AlertCircle, CheckCircle, Clock, Loader2, Shield } from 'lucide-react';

import { updateVerificationInfo } from '../_actions/profile.action';

import type { Profile } from '@package/db/core/models/account.model';

interface ProfileVerificationProps {
  profile: Profile;
}

const countries = [
  'United States',
  'Canada',
  'United Kingdom',
  'Australia',
  'Germany',
  'France',
  'Italy',
  'Spain',
  'Netherlands',
  'Belgium',
  'Switzerland',
  'Austria',
  'Sweden',
  'Norway',
  'Denmark',
  'Finland',
  'Ireland',
  'Portugal',
  'Greece',
  'Poland',
  'Czech Republic',
  'Hungary',
  'Slovakia',
  'Slovenia',
  'Croatia',
  'Romania',
  'Bulgaria',
  'Lithuania',
  'Latvia',
  'Estonia',
  'Luxembourg',
  'Malta',
  'Cyprus',
  'Japan',
  'South Korea',
  'Singapore',
  'Hong Kong',
  'Taiwan',
  'New Zealand',
  'Israel',
  'United Arab Emirates',
  'Saudi Arabia',
  'Qatar',
  'Kuwait',
  'Bahrain',
  'Oman',
  'Jordan',
  'Lebanon',
  'Turkey',
  'Egypt',
  'South Africa',
  'Nigeria',
  'Kenya',
  'Ghana',
  'Morocco',
  'Tunisia',
  'Algeria',
  'Brazil',
  'Argentina',
  'Chile',
  'Colombia',
  'Peru',
  'Ecuador',
  'Uruguay',
  'Paraguay',
  'Bolivia',
  'Venezuela',
  'Mexico',
  'Costa Rica',
  'Panama',
  'Guatemala',
  'Honduras',
  'El Salvador',
  'Nicaragua',
  'Dominican Republic',
  'Jamaica',
  'Trinidad and Tobago',
  'Barbados',
  'India',
  'China',
  'Thailand',
  'Malaysia',
  'Indonesia',
  'Philippines',
  'Vietnam',
  'Cambodia',
  'Laos',
  'Myanmar',
  'Bangladesh',
  'Sri Lanka',
  'Nepal',
  'Bhutan',
  'Maldives',
  'Pakistan',
  'Afghanistan',
  'Iran',
  'Iraq',
  'Russia',
  'Ukraine',
  'Belarus',
  'Moldova',
  'Georgia',
  'Armenia',
  'Azerbaijan',
  'Kazakhstan',
  'Uzbekistan',
  'Turkmenistan',
  'Kyrgyzstan',
  'Tajikistan',
  'Mongolia',
  'North Korea',
  'Other',
].sort();

export function ProfileVerification({ profile }: ProfileVerificationProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data with existing profile data
  const [formData, setFormData] = useState({
    name: profile.name || '',
    phone: profile.verificationInfo?.phone || '',
    dateOfBirth: profile.verificationInfo?.dateOfBirth || '',
    address: {
      street:
        profile.verificationInfo?.address?.street ||
        profile.address?.street ||
        '',
      city:
        profile.verificationInfo?.address?.city || profile.address?.city || '',
      state:
        profile.verificationInfo?.address?.state ||
        profile.address?.state ||
        '',
      zipCode:
        profile.verificationInfo?.address?.zipCode ||
        profile.address?.zipCode ||
        '',
      country:
        profile.verificationInfo?.address?.country ||
        profile.address?.country ||
        '',
    },
  });

  // Check if sections are verified (admin-only checkboxes)
  const isNameVerified = profile.verificationInfo?.nameVerified || false;
  const isContactInfoVerified =
    profile.verificationInfo?.contactInfoVerified || false;
  const isAddressVerified = profile.verificationInfo?.addressVerified || false;

  const handleSubmitVerification = async () => {
    setIsSubmitting(true);

    try {
      const result = await updateVerificationInfo(formData);

      if (result.success) {
        toast({
          title: 'Verification information updated',
          description:
            'Your verification information has been saved successfully.',
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description:
            result.error || 'Failed to update verification information',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Verification Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Verification Status
          </CardTitle>
          <CardDescription>
            Complete your verification to build trust and unlock additional
            features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="p-4 text-center border rounded-lg">
              <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
              <h4 className="font-medium">Email Verified</h4>
              <p className="text-sm text-muted-foreground">
                Your email is confirmed
              </p>
            </div>
            <div className="p-4 text-center border rounded-lg">
              {isNameVerified ? (
                <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
              ) : (
                <Clock className="w-8 h-8 mx-auto mb-2 text-yellow-500" />
              )}
              <h4 className="font-medium">Name & DOB</h4>
              <p className="text-sm text-muted-foreground">
                {isNameVerified ? 'Verified' : 'Complete form below'}
              </p>
            </div>
            <div className="p-4 text-center border rounded-lg">
              {isAddressVerified ? (
                <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-500" />
              ) : (
                <AlertCircle className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              )}
              <h4 className="font-medium">Address</h4>
              <p className="text-sm text-muted-foreground">
                {isAddressVerified ? 'Verified' : 'Complete address below'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Name and Date of Birth */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isNameVerified && (
              <CheckCircle className="w-5 h-5 text-green-500" />
            )}
            Name & Date of Birth
          </CardTitle>
          <CardDescription>
            Provide your name and date of birth for verification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                placeholder="Enter your full legal name"
                disabled={isNameVerified}
              />
              {isNameVerified && (
                <div className="flex items-center gap-1 text-sm text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  Verified by admin
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="dateOfBirth">Date of Birth</Label>
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) =>
                  setFormData({ ...formData, dateOfBirth: e.target.value })
                }
                disabled={isNameVerified}
              />
              {isNameVerified && (
                <div className="flex items-center gap-1 text-sm text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  Verified by admin
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isContactInfoVerified && (
              <CheckCircle className="w-5 h-5 text-green-500" />
            )}
            Contact Information
          </CardTitle>
          <CardDescription>
            Provide your contact details for verification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              placeholder="+****************"
              disabled={isContactInfoVerified}
            />
            {isContactInfoVerified && (
              <div className="flex items-center gap-1 text-sm text-green-600">
                <CheckCircle className="w-4 h-4" />
                Verified by admin
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isAddressVerified && (
              <CheckCircle className="w-5 h-5 text-green-500" />
            )}
            Address Information
          </CardTitle>
          <CardDescription>
            Provide your address for verification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="street">Street Address</Label>
            <Input
              id="street"
              value={formData.address.street}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  address: { ...formData.address, street: e.target.value },
                })
              }
              placeholder="123 Main Street"
              disabled={isAddressVerified}
            />
            {isAddressVerified && (
              <div className="flex items-center gap-1 text-sm text-green-600">
                <CheckCircle className="w-4 h-4" />
                Verified by admin
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                value={formData.address.city}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    address: { ...formData.address, city: e.target.value },
                  })
                }
                placeholder="New York"
                disabled={isAddressVerified}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state">State/Province</Label>
              <Input
                id="state"
                value={formData.address.state}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    address: { ...formData.address, state: e.target.value },
                  })
                }
                placeholder="NY"
                disabled={isAddressVerified}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="zipCode">ZIP/Postal Code</Label>
              <Input
                id="zipCode"
                value={formData.address.zipCode}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    address: { ...formData.address, zipCode: e.target.value },
                  })
                }
                placeholder="10001"
                disabled={isAddressVerified}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Select
                value={formData.address.country}
                onValueChange={(value) =>
                  setFormData({
                    ...formData,
                    address: { ...formData.address, country: value },
                  })
                }
                disabled={isAddressVerified}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Card>
        <CardContent className="pt-6">
          <Button
            onClick={handleSubmitVerification}
            disabled={
              isSubmitting ||
              (isNameVerified && isContactInfoVerified && isAddressVerified)
            }
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              <>
                <Shield className="w-4 h-4 mr-2" />
                {isNameVerified && isContactInfoVerified && isAddressVerified
                  ? 'All Information Verified'
                  : 'Update Verification Information'}
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Verification Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Benefits</CardTitle>
          <CardDescription>Why verify your account?</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="flex items-center gap-2 font-medium">
                <Shield className="w-4 h-4 text-green-500" />
                Increased Trust
              </h4>
              <p className="text-sm text-muted-foreground">
                Verified users are more trusted by other marketplace members
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="flex items-center gap-2 font-medium">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Higher Limits
              </h4>
              <p className="text-sm text-muted-foreground">
                Access to higher transaction limits and premium features
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="flex items-center gap-2 font-medium">
                <CheckCircle className="w-4 h-4 text-green-500" />
                Better Visibility
              </h4>
              <p className="text-sm text-muted-foreground">
                Verified listings appear higher in search results
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="flex items-center gap-2 font-medium">
                <Shield className="w-4 h-4 text-green-500" />
                Payment Protection
              </h4>
              <p className="text-sm text-muted-foreground">
                Enhanced buyer and seller protection for transactions
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
