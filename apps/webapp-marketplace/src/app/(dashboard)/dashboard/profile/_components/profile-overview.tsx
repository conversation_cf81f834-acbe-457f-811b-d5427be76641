'use client';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CalendarDays, Mail, MapPin, User, Shield, Star } from 'lucide-react';
import Image from 'next/image';

import type { Profile } from '@package/db/core/models/account.model';

interface ProfileOverviewProps {
  profile: Profile;
}

export function ProfileOverview({ profile }: ProfileOverviewProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
      {/* Main Profile Card */}
      <Card className="md:col-span-2">
        <CardHeader className="pb-4">
          <div className="flex items-start gap-4">
            <div className="relative w-20 h-20 overflow-hidden rounded-full bg-muted">
              {profile.image ? (
                <Image
                  src={profile.image}
                  alt={profile.name || 'Profile picture'}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full bg-primary/10 text-primary">
                  <User size={40} />
                </div>
              )}
            </div>
            <div className="flex-1">
              <CardTitle className="text-2xl">
                {profile.name || 'User'}
              </CardTitle>
              <CardDescription className="flex items-center mt-1">
                <Mail className="w-4 h-4 mr-2" />
                {profile.email}
              </CardDescription>
              <div className="flex gap-2 mt-3">
                <Badge
                  variant="outline"
                  className="text-green-700 border-green-200 bg-green-50"
                >
                  <Shield className="w-3 h-3 mr-1" />
                  Active
                </Badge>
                <Badge
                  variant="outline"
                  className="text-blue-700 border-blue-200 bg-blue-50"
                >
                  {profile.role || 'Member'}
                </Badge>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">
                Account Information
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Member ID</p>
                  <p className="font-mono text-sm text-muted-foreground">
                    {profile.id.substring(0, 8)}...
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Member Since</p>
                  <div className="flex items-center">
                    <CalendarDays className="w-4 h-4 mr-2 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {new Date(profile.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">
                Profile Completion
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Profile completeness</span>
                  <span className="font-medium">
                    {calculateProfileCompleteness(profile)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${calculateProfileCompleteness(profile)}%`,
                    }}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Complete your profile to build trust with other users
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats Card */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Stats</CardTitle>
          <CardDescription>Your marketplace activity</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Listings Created</span>
              <span className="text-2xl font-bold">0</span>
            </div>
            <p className="text-xs text-muted-foreground">
              Items you've listed for sale
            </p>
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Reviews Received</span>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="text-2xl font-bold">0</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Feedback from other users
            </p>
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Transactions</span>
              <span className="text-2xl font-bold">0</span>
            </div>
            <p className="text-xs text-muted-foreground">
              Completed purchases and sales
            </p>
          </div>

          <div className="pt-4 border-t">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">
                Location
              </p>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-muted-foreground" />
                <p className="text-sm">Not specified</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function calculateProfileCompleteness(profile: Profile): number {
  let completeness = 0;
  const fields = [
    profile.name,
    profile.email,
    profile.image,
    // Add more fields as needed
  ];

  const filledFields = fields.filter(field => field && field.trim() !== '').length;
  completeness = Math.round((filledFields / fields.length) * 100);

  return Math.min(completeness, 100);
}
