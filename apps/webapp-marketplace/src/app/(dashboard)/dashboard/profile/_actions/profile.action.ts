'use server';

import { revalidatePath } from 'next/cache';

import { auth } from '../../../../../libs/security/auth';

import { backendConfig } from '@package/configs';
import {
  getAccountService,
  getFeedbackService,
  getListingService,
} from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import type {
  Profile,
  UpdateProfileInput,
} from '@package/db/core/models/account.model';
import type { Feedback } from '@package/db/core/models/feedback.model';
import { createS3Service } from '@package/s3';

/**
 * Get current user profile
 */
export async function getCurrentProfile(): Promise<Profile | null> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return null;
    }

    const accountService = getAccountService();
    return await accountService.getProfileByEmail(session.user.email);
  } catch (error) {
    console.error('Error fetching profile:', error);
    return null;
  }
}

/**
 * Update user profile
 */
export async function updateProfile(
  data: UpdateProfileInput
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return { success: false, error: 'Not authenticated' };
    }

    const accountService = getAccountService();
    await accountService.updateProfileByEmail(session.user.email, data);

    revalidatePath('/dashboard/profile');
    return { success: true };
  } catch (error) {
    console.error('Error updating profile:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to update profile',
    };
  }
}

/**
 * Upload profile photo
 */
export async function uploadProfilePhoto(
  formData: FormData
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return { success: false, error: 'Not authenticated' };
    }

    const file = formData.get('file') as File;
    if (!file) {
      return { success: false, error: 'No file provided' };
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image.',
      };
    }

    // Validate file size (max 5MB for profile photos)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 5MB.' };
    }

    // Upload to S3
    const s3Service = createS3Service({
      region: backendConfig.aws.s3.region,
      bucket: backendConfig.aws.s3.bucket,
      credentials: backendConfig.aws.s3.credentials,
    });

    const url = await s3Service.uploadFile(file, `profiles/${session.user.id}`);

    // Update profile with new image URL
    const accountService = getAccountService();
    await accountService.updateProfileByEmail(session.user.email, {
      image: url,
    });

    revalidatePath('/dashboard/profile');
    return { success: true, url };
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to upload photo',
    };
  }
}

/**
 * Get user reviews/feedback
 */
export async function getUserReviews(): Promise<Feedback[]> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return [];
    }

    const feedbackService = getFeedbackService();
    return await feedbackService.getFeedbackByProfileOrStoreId(session.user.id);
  } catch (error) {
    console.error('Error fetching user reviews:', error);
    return [];
  }
}

/**
 * Update user address
 */
export async function updateAddress(address: {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return { success: false, error: 'Not authenticated' };
    }

    const accountService = getAccountService();
    await accountService.updateProfileByEmail(session.user.email, {
      address,
    });

    revalidatePath('/dashboard/profile');
    return { success: true };
  } catch (error) {
    console.error('Error updating address:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to update address',
    };
  }
}

/**
 * Submit verification documents
 */
export async function submitVerificationDocuments(
  formData: FormData
): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return { success: false, error: 'Not authenticated' };
    }

    const documentType = formData.get('documentType') as string;
    const file = formData.get('file') as File;

    if (!file || !documentType) {
      return { success: false, error: 'Document type and file are required' };
    }

    // Validate file type (images and PDFs)
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'application/pdf',
    ];
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: 'Invalid file type. Please upload an image or PDF.',
      };
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { success: false, error: 'File too large. Maximum size is 10MB.' };
    }

    // Upload to S3 in verification folder
    const s3Service = createS3Service({
      region: backendConfig.aws.s3.region,
      bucket: backendConfig.aws.s3.bucket,
      credentials: backendConfig.aws.s3.credentials,
    });

    const url = await s3Service.uploadFile(
      file,
      `verification/${session.user.id}`
    );

    // Get current profile to append to existing verification documents
    const accountService = getAccountService();
    const currentProfile = await accountService.getProfileByEmail(
      session.user.email
    );

    const newDocument = {
      type: documentType,
      url,
      status: 'pending' as const,
      uploadedAt: new Date().toISOString(),
    };

    const existingDocuments = currentProfile.verificationDocuments || [];
    const updatedDocuments = [...existingDocuments, newDocument];

    // Update profile with new verification document
    await accountService.updateProfileByEmail(session.user.email, {
      verificationDocuments: updatedDocuments,
      verificationStatus: 'pending',
    });

    revalidatePath('/dashboard/profile');
    return { success: true };
  } catch (error) {
    console.error('Error submitting verification documents:', error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to submit documents',
    };
  }
}

/**
 * Update verification information
 */
export async function updateVerificationInfo(verificationData: {
  name: string;
  phone: string;
  dateOfBirth: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return { success: false, error: 'Not authenticated' };
    }

    const accountService = getAccountService();
    const currentProfile = await accountService.getProfileByEmail(
      session.user.email
    );

    // Preserve existing verification status (admin-only fields)
    const existingVerificationInfo = currentProfile.verificationInfo || {};

    const updatedVerificationInfo = {
      ...existingVerificationInfo,
      phone: verificationData.phone,
      dateOfBirth: verificationData.dateOfBirth,
      address: verificationData.address,
      // Keep existing admin verification flags
      nameVerified: existingVerificationInfo.nameVerified || false,
      contactInfoVerified:
        existingVerificationInfo.contactInfoVerified || false,
      addressVerified: existingVerificationInfo.addressVerified || false,
    };

    // Update both the name field and verification info
    await accountService.updateProfileByEmail(session.user.email, {
      name: verificationData.name,
      verificationInfo: updatedVerificationInfo,
    });

    revalidatePath('/dashboard/profile');
    return { success: true };
  } catch (error) {
    console.error('Error updating verification information:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update verification information',
    };
  }
}

/**
 * Get user listings
 */
export async function getUserListings() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return [];
    }

    const config = backendConfig.aws;
    const service = getListingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    const listings = await service.getListingsByUserId(session.user.id);
    return listings;
  } catch (error) {
    console.error('Error getting user listings:', error);
    return [];
  }
}
