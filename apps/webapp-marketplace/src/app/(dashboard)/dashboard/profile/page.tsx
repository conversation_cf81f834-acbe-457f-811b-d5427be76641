import { Suspense } from 'react';

import { getCurrentProfile, getUserReviews } from './_actions/profile.action';
import { ProfileTabs } from './_components/profile-tabs';

export default async function ProfilePage() {
  const [profile, reviews] = await Promise.all([
    getCurrentProfile(),
    getUserReviews(),
  ]);

  if (!profile) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
          <p className="mt-2 text-gray-600">
            Please sign in to view your profile.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
        <p className="mt-2 text-gray-600">
          Manage your account settings and preferences
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <ProfileTabs profile={profile} reviews={reviews} />
      </Suspense>
    </div>
  );
}
