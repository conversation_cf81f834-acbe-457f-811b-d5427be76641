import { NextRequest, NextResponse } from 'next/server';
import { backendConfig } from '@package/configs';
import { getDynamoDBClient } from '@package/db';
import { getListingService } from '@package/db';

export async function POST(request: NextRequest) {
  try {
    // Simple security check
    const authHeader = request.headers.get('authorization');
    if (authHeader !== 'Bearer admin-secret') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🚀 Starting GSI fix via ElectroDB...');

    const config = backendConfig.aws;
    const service = getListingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    // Get all listings using the working scan method
    const allListings = await service.listListings(1000); // Get up to 1000 listings
    
    console.log(`📊 Found ${allListings.length} listings to process`);

    if (allListings.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No listings found to update',
        updated: 0,
        total: 0
      });
    }

    let updated = 0;
    const errors: any[] = [];

    // Update each listing - this will trigger ElectroDB to populate GSI fields correctly
    for (const listing of allListings) {
      try {
        // Update the listing with itself - this forces ElectroDB to recalculate GSI keys
        await service.updateListingById(listing.listingId, {
          name: listing.name,
          description: listing.description,
          price: listing.price,
          categoryId: listing.categoryId,
          status: listing.status,
          images: listing.images,
          type: listing.type,
          condition: listing.condition,
          attributes: listing.attributes || [],
          rentalUnit: listing.rentalUnit,
          rentalAvailability: listing.rentalAvailability || [],
        });
        
        updated++;
        
        if (updated % 10 === 0) {
          console.log(`📝 Updated ${updated}/${allListings.length} listings...`);
        }
      } catch (error) {
        console.error(`❌ Failed to update listing ${listing.listingId}:`, error);
        errors.push({ listingId: listing.listingId, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }

    console.log(`✅ Successfully updated ${updated} listings`);
    console.log('🎉 GSI fix complete!');

    return NextResponse.json({
      success: true,
      message: 'GSI fix completed successfully',
      updated,
      total: allListings.length,
      errors: errors.length,
      errorDetails: errors
    });

  } catch (error) {
    console.error('❌ GSI fix failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'GSI Fix Endpoint',
    usage: 'POST with Authorization: Bearer admin-secret',
    description: 'Updates all listings through ElectroDB to populate GSI fields correctly'
  });
}
