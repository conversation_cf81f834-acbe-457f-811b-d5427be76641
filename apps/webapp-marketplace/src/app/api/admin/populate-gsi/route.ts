import { backendConfig } from '@package/configs';
import { populateGSIFields } from '@package/db/scripts/populate-gsi';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Simple security check - you can enhance this
    const authHeader = request.headers.get('authorization');
    if (authHeader !== 'Bearer admin-secret') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🚀 Starting GSI population via API...');

    const config = {
      region: backendConfig.aws.region,
      endpoint: backendConfig.aws.endpoint,
      accessKeyId: backendConfig.aws.credentials.accessKeyId,
      secretAccessKey: backendConfig.aws.credentials.secretAccessKey,
      tableName: backendConfig.aws.dynamodb.tables.core,
    };

    const result = await populateGSIFields(config);

    return NextResponse.json({
      success: true,
      message: 'GSI population completed successfully',
      ...result,
    });
  } catch (error) {
    console.error('❌ GSI population failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'GSI Population Endpoint',
    usage: 'POST with Authorization: Bearer admin-secret',
  });
}
