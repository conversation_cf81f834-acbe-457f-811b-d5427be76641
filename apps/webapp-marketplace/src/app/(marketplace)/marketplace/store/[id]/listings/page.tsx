import { notFound } from 'next/navigation';

import {
  Calendar,
  Mail,
  MapPin,
  Package,
  Shield,
  Star,
  Tag,
  User,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { edgeConfig } from '@package/configs';

import ContactForm from './_components/contact-form';
import FeedbackFormWrapper from './_components/feedback-form-wrapper';
import ListingCard from './_components/listings';

import { Section } from '../../../../../_components/section';
import {
  getFeedbackByProfileOrStoreId,
  getListingsByUserId,
  getProfileById,
  getUserById,
} from '../../../_actions';

import { auth } from '../../../../../../libs/security/auth';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{
    category?: string;
    sort?: string;
  }>;
}

export default async function Page({ params, searchParams }: PageProps) {
  const session = await auth();

  const { featureFlags } = edgeConfig;

  const sellerId = (await params).id;
  const _searchParams = await searchParams;
  let seller = await getProfileById(sellerId);

  // Fallback to basic user data if profile not found
  if (!seller) {
    console.warn(
      `Profile not found for user ${sellerId}, falling back to basic user data`
    );
    const basicUser = await getUserById(sellerId);
    seller = basicUser
      ? ({
          ...basicUser,
          createdAt: basicUser.createdAt?.toString() || '',
          type: 'USER',
          role: basicUser.role || 'USER',
          password: '', // Not needed for display
        } as any)
      : null;
  }

  if (!seller) {
    notFound();
  }

  let listings = await getListingsByUserId(sellerId);

  // Fetch feedback for this store
  const feedbackList = await getFeedbackByProfileOrStoreId(sellerId);

  // Calculate ratings from feedback
  const ratings = feedbackList.length
    ? {
        average:
          feedbackList.reduce((sum, f) => sum + (f.rating || 0), 0) /
          feedbackList.length,
        count: feedbackList.length,
        breakdown: feedbackList.reduce((acc, f) => {
          acc[f.rating as number] = (acc[f.rating as number] || 0) + 1;
          return acc;
        }, {} as Record<number, number>),
      }
    : {
        average: 0,
        count: 0,
        breakdown: {},
      };

  // Sort options
  const sortOption = _searchParams.sort || 'newest';
  if (sortOption === 'newest') {
    listings = listings.sort((a, b) => {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateB.getTime() - dateA.getTime();
    });
  } else if (sortOption === 'price-low') {
    listings = listings.sort((a, b) => a.price - b.price);
  } else if (sortOption === 'price-high') {
    listings = listings.sort((a, b) => b.price - a.price);
  }

  // Filter by category if provided
  if (_searchParams.category) {
    listings = listings.filter(
      (listing) => listing.categoryId === _searchParams.category
    );
  }

  // Format date
  const joinDate = new Date(seller.createdAt || Date.now()).toLocaleDateString(
    'en-US',
    {
      year: 'numeric',
      month: 'long',
    }
  );

  // Get all unique categories from listings
  const categories = [
    ...new Set(listings.map((listing) => listing.categoryId)),
  ];

  // TODO implement stats
  const stats = {
    responseRate: '98%',
    responseTime: 'Within 2 hours',
    totalListings: listings.length,
    memberSince: joinDate,
  };

  return (
    <>
      <div className="bg-muted/30">
        <Section className="py-8">
          <div className="flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="w-20 h-20 border-2 border-primary/20">
                <AvatarImage
                  src={seller?.image}
                  alt={seller?.name || 'Unknown'}
                />
                <AvatarFallback className="bg-primary/10">
                  <User className="w-10 h-10 text-primary/70" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold">
                  {seller?.name || 'Unknown'}
                </h1>
                <div className="flex items-center mt-1 gap-1.5">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-4 h-4 ${
                          star <= Math.round(ratings.average)
                            ? 'text-yellow-500 fill-yellow-500'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm font-medium">
                    {ratings.average.toFixed(2)}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    ({ratings.count} reviews)
                  </span>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  <Badge variant="secondary" className="font-normal">
                    <MapPin className="w-3 h-3 mr-1" />
                    {seller?.verificationInfo?.address?.city &&
                    seller?.verificationInfo?.address?.country
                      ? `${seller.verificationInfo.address.city}, ${seller.verificationInfo.address.country}`
                      : seller?.address?.city && seller?.address?.country
                      ? `${seller.address.city}, ${seller.address.country}`
                      : 'Location not specified'}
                  </Badge>
                  <Badge variant="secondary" className="font-normal">
                    <Calendar className="w-3 h-3 mr-1" />
                    Member since {joinDate}
                  </Badge>

                  <Badge
                    variant="secondary"
                    className="font-normal text-green-700 bg-green-500/10"
                  >
                    <Shield className="w-3 h-3 mr-1" />
                    Verified Seller
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </Section>
      </div>

      <Section className="py-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {/* Sidebar with seller info */}
          <div className="space-y-6 md:col-span-1">
            {/* About section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" /> About the Seller
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {featureFlags.profile.enableAbout && (
                  <p className="text-sm text-muted-foreground">
                    {
                      "Welcome to my store! I'm passionate about sustainability and giving items a second life. Feel free to browse my listings and reach out with any questions."
                    }
                  </p>
                )}

                <Separator />

                <div className="grid grid-cols-2 gap-y-3">
                  {featureFlags.profile.enableStats && (
                    <>
                      <div className="text-sm text-muted-foreground">
                        Response Rate
                      </div>
                      <div className="text-sm font-medium text-right">
                        {stats.responseRate}
                      </div>

                      <div className="text-sm text-muted-foreground">
                        Response Time
                      </div>
                      <div className="text-sm font-medium text-right">
                        {stats.responseTime}
                      </div>
                    </>
                  )}
                  <div className="text-sm text-muted-foreground">
                    Total Listings
                  </div>
                  <div className="text-sm font-medium text-right">
                    {stats.totalListings}
                  </div>

                  <div className="text-sm text-muted-foreground">
                    Member Since
                  </div>
                  <div className="text-sm font-medium text-right">
                    {stats.memberSince}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ratings section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5" /> Seller Ratings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="flex flex-col items-center justify-center p-3 border rounded-lg">
                    <span className="text-3xl font-bold text-primary">
                      {ratings.average.toFixed(1)}
                    </span>
                    <div className="flex my-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-3 h-3 ${
                            star <= Math.round(ratings.average)
                              ? 'text-yellow-500 fill-yellow-500'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {ratings.count} reviews
                    </span>
                  </div>
                  <div className="flex-1 space-y-1">
                    {[5, 4, 3, 2, 1].map((rating) => (
                      <div key={rating} className="flex items-center gap-2">
                        <div className="w-3 text-xs font-medium">{rating}</div>
                        <div className="w-full h-2 overflow-hidden rounded-full bg-muted">
                          <div
                            className="h-full bg-primary"
                            style={{
                              width: `${
                                ((ratings.breakdown[rating] || 0) /
                                  (ratings.count || 1)) *
                                100
                              }%`,
                            }}
                          />
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {ratings.breakdown[rating] || 0}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Feedback comments */}
                {feedbackList.length > 0 && (
                  <div className="mt-6">
                    <div className="mb-2 font-semibold">Recent Feedback</div>
                    <div className="space-y-4 overflow-y-auto max-h-60">
                      {feedbackList.map((fb) => (
                        <div key={fb.feedbackId} className="pb-2 border-b">
                          <div className="flex items-center gap-2">
                            {[...Array(fb.rating)].map((_, i) => (
                              <Star
                                key={`star-${fb.feedbackId}-${i}`}
                                className="w-3 h-3 text-yellow-500 fill-yellow-500"
                              />
                            ))}
                            <span className="ml-2 text-xs text-muted-foreground">
                              {new Date(
                                Number(fb.createdAt)
                              ).toLocaleDateString()}
                            </span>
                          </div>
                          {fb.comment && (
                            <div className="mt-1 text-sm">{fb.comment}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Leave Feedback */}
            {session && session?.user?.id !== sellerId && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="w-5 h-5" /> Leave Feedback
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FeedbackFormWrapper sellerId={sellerId} />
                </CardContent>
              </Card>
            )}

            {/* Contact form */}
            {session && featureFlags.profile.enableMessageProfile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="w-5 h-5" /> Contact{' '}
                    {seller?.name || 'Unknown'}
                  </CardTitle>
                  <CardDescription>
                    Send a message directly to this seller
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ContactForm sellerId={sellerId} />
                </CardContent>
              </Card>
            )}
          </div>

          {/* content section */}
          <div className="md:col-span-2">
            <Tabs defaultValue="all" className="w-full">
              <div className="flex flex-col gap-4 mb-6 sm:flex-row sm:items-center sm:justify-between">
                <TabsList className="w-full sm:w-auto">
                  <TabsTrigger value="all">
                    All Items ({listings.length})
                  </TabsTrigger>
                  {/* <TabsTrigger value="active">Active</TabsTrigger>
                  {categories.length > 0 && (
                    <TabsTrigger value="categories">Categories</TabsTrigger>
                  )} */}
                </TabsList>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    Sort by:
                  </span>
                  <Select defaultValue={sortOption}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest">Newest First</SelectItem>
                      <SelectItem value="price-low">
                        Price: Low to High
                      </SelectItem>
                      <SelectItem value="price-high">
                        Price: High to Low
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <TabsContent value="all" className="m-0">
                {listings.length > 0 ? (
                  <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {listings.map((listing) => (
                      <ListingCard key={listing.listingId} listing={listing} />
                    ))}
                  </div>
                ) : (
                  <Card className="border-dashed">
                    <CardContent className="flex flex-col items-center justify-center pt-6 pb-8">
                      <Package className="w-10 h-10 mb-4 text-muted-foreground" />
                      <h3 className="font-semibold">No listings found</h3>
                      <p className="mt-1 text-sm text-muted-foreground">
                        This seller doesn't have any active listings right now.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="active" className="m-0">
                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {listings
                    .filter((listing) => listing.status === 'ACTIVE')
                    .map((listing) => (
                      <ListingCard key={listing.listingId} listing={listing} />
                    ))}
                </div>
              </TabsContent>

              <TabsContent value="categories" className="m-0">
                <div className="space-y-8">
                  {categories.map((categoryId) => {
                    const categoryListings = listings.filter(
                      (listing) => listing.categoryId === categoryId
                    );
                    return (
                      <div key={categoryId}>
                        <h3 className="flex items-center mb-4 text-lg font-semibold">
                          <Tag className="w-4 h-4 mr-2" />
                          {categoryId}
                        </h3>
                        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                          {categoryListings.map((listing) => (
                            <ListingCard
                              key={listing.listingId}
                              listing={listing}
                            />
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </Section>
    </>
  );
}
