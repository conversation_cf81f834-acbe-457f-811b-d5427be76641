'use server';

import { backendConfig, edgeConfig } from '@package/configs';
import { getMessageService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import {
  MessageStatus,
  MessageType,
  type Profile,
} from '@package/db/core/models';
import { mailerService } from '@package/mailer';

import { auth } from '../../../../../../../libs/security/auth';

export async function createMessage(
  receiver: Profile,
  listingId: string,
  listingName: string,
  message: string
) {
  const config = backendConfig.aws;
  const service = getMessageService(
    'dynamodb',
    config.dynamodb.tables.account,
    getDynamoDBClient()
  );

  const session = await auth();
  const sender = session?.user;

  const subject = `${listingName}`;
  const emailContent = `
Hello,<br /><br />

You've received a new message regarding the listing: <b>${listingName}</b>.<br /><br />

Please check your messages for more details: <br />
<a href="${edgeConfig.baseUrl}${edgeConfig.routes.dashboard.messages}">${edgeConfig.baseUrl}${edgeConfig.routes.dashboard.messages}</a>

<br /><br />
Thank you,<br />
Circular Marketplace Team
`;
  const conversationId = `${listingId}#${sender?.id}#${receiver.id}`;

  try {
    // TODO - only return the conversationId if it exists
    // TODO - handle spam messages
    const conversationExists = await service.getConversation(
      sender?.id as string,
      receiver.id,
      listingId
    );

    if (!Object.keys(conversationExists).length) {
      const createConversationResult = await service.createConversation({
        status: MessageStatus.UNREAD,
        type: MessageType.CONVERSATION,
        senderId: sender?.id as string,
        receiverId: receiver.id,
        content: message,
        messageOwnerId: sender?.id as string,
        listingId,
        subject,
        conversationId,
      });

      if (!createConversationResult) {
        console.error('Error creating conversation');
        return null;
      }
    }

    const createMessageResult = await service.createMessage({
      status: MessageStatus.UNREAD,
      type: MessageType.MESSAGE,
      senderId: sender?.id as string,
      receiverId: receiver.id,
      messageOwnerId: sender?.id as string,
      content: message,
      listingId,
      conversationId,
    });

    if (!createMessageResult) {
      console.error('Error creating message');
      return null;
    }

    // Send email notification
    const emailResult = await mailerService().sendMessage(
      receiver.email,
      `New Message About The Listing: ${subject}`,
      emailContent
    );

    if (!emailResult) {
      console.error('Error sending email notification');
      return null;
    }

    return createMessageResult;
  } catch (error) {
    console.error('Error creating message:', JSON.stringify(error));
    return null;
  }
}
