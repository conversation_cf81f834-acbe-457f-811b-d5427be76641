'use client';

import {
  Calendar,
  Clock,
  Edit,
  Gift,
  Handshake,
  MessageCircle,
  Package,
  Tag,
  Wallet,
} from 'lucide-react';
import moment from 'moment';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

import {
  type Feedback,
  type Listing,
  ListingCondition,
  ListingStatus,
  ListingType,
  type Profile,
} from '@package/db/core/models';

import { ItemBookingDialog } from './item-booking-dialog';
import { ItemContactSeller } from './item-contact-seller';
import { ItemDescription } from './item-description';
import InformationSafety from './item-info-safety';
import { ItemInformation } from './item-information';
import ItemRelatedListings from './item-listing-related';
import { ItemMainImage } from './item-main-image';
import InformationSeller from './item-seller';
import { ItemShippingPolicies } from './item-shipping-policies';
import { ItemThumbnails } from './item-thumbnails';

import { edgeConfig } from '@package/configs';

type MainImage = Listing['images'][number];

function formatDate(dateString?: string): string {
  if (!dateString) return 'Recently';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

function getConditionLabel(condition: ListingCondition): string {
  switch (condition) {
    case ListingCondition.NEW:
      return 'Brand New';
    case ListingCondition.LIKE_NEW:
      return 'Like New';
    case ListingCondition.GOOD:
      return 'Good';
    case ListingCondition.FAIR:
      return 'Fair';
    case ListingCondition.POOR:
      return 'Poor';
    default:
      return condition;
  }
}

function getConditionColor(condition: ListingCondition): string {
  switch (condition) {
    case ListingCondition.NEW:
      return 'bg-green-100 text-green-800 border-green-200';
    case ListingCondition.LIKE_NEW:
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case ListingCondition.GOOD:
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case ListingCondition.FAIR:
      return 'bg-amber-100 text-amber-800 border-amber-200';
    case ListingCondition.POOR:
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

interface ItemDetailsProps {
  listing: Listing;
  userListings: Listing[];
  seller: Profile | null;
  relatedListings: Listing[];
  enableContactSeller?: boolean;
  enableAddToCart?: boolean;
  sellerFeedback: Feedback[];
}

// Helper to display rental availability exactly as set in listing-edit.tsx, using 24-hour format and parseZone for local time
function formatRentalDate(dateString: string) {
  // Use moment.parseZone to ensure no timezone shift, matching datetime-local input
  return moment.parseZone(dateString).format('YYYY-MM-DD HH:mm');
}

export default function ItemDetails({
  listing,
  userListings,
  seller,
  relatedListings,
  enableContactSeller,
  enableAddToCart,
  sellerFeedback,
}: ItemDetailsProps) {
  const { data: session } = useSession();
  const router = useRouter();

  const [mainImage, setMainImage] = useState<MainImage>(
    listing.images.find((image) => image.isMain) || listing.images[0]
  );
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);

  // EVENTS

  const handleClickImage = (image: MainImage) => {
    setMainImage(image);
  };

  // Get attribute values by key with default fallbacks
  const getAttributeValue = (key: string, defaultValue = 'Not specified') => {
    if (!listing.attributes) return defaultValue;
    const attr = listing.attributes.find((attr) => attr.key === key);
    return attr ? attr.value : defaultValue;
  };

  const conditionSummary = getAttributeValue('conditionSummary', '');
  const deliveryOptions = getAttributeValue('deliveryOptions', '').split(',');
  const safetyRequirements = getAttributeValue('safetyRequirements', '');

  // Render the listing action buttons (Book Now, Contact Seller, etc.)
  const actionComponent = () => (
    <>
      {listing.userId === session?.user?.id ? (
        <Button
          className="w-full mb-3"
          onClick={() => {
            router.push(`/marketplace/item/${listing.listingId}/edit`);
            router.refresh();
          }}
        >
          <Edit className="w-4 h-4 mr-2" /> Edit Listing
        </Button>
      ) : (
        <>
          {listing.status !== ListingStatus.ACTIVE && (
            <Button className="w-full mb-3" variant={'outline'}>
              Unavailable
            </Button>
          )}

          {listing.status === ListingStatus.ACTIVE &&
            listing.type === ListingType.RENT &&
            seller && (
              <Button
                className="w-full mb-3"
                onClick={() => setIsBookingOpen(true)}
              >
                {/* FIXME: disable when booking approval is pending */}
                <Calendar className="w-4 h-4 mr-2" /> Book Now
              </Button>
            )}

          {enableContactSeller && seller && (
            <Button
              className="w-full mb-3"
              onClick={() => setIsContactFormOpen(true)}
            >
              <MessageCircle className="w-4 h-4 mr-2" /> Contact Seller
            </Button>
          )}

          {enableAddToCart && (
            <Button variant="outline" className="w-full">
              <Package className="w-4 h-4 mr-2" /> Add to Cart
            </Button>
          )}
        </>
      )}
    </>
  );

  // Renders price information and type badge
  const priceInfoComponent = () => (
    <div className="flex items-center justify-between mb-6">
      <span className="text-3xl font-bold">${listing.price.toFixed(2)}</span>
      <Badge
        className="flex items-center px-3 py-1.5 text-sm"
        variant="secondary"
      >
        {(() => {
          switch (listing.type) {
            case ListingType.SELL:
              return <Tag className="w-4 h-4 mr-2" aria-label="Sell icon" />;
            case ListingType.FREE:
              return <Gift className="w-4 h-4 mr-2" aria-label="Free icon" />;
            case ListingType.RENT:
              return <Clock className="w-4 h-4 mr-2" aria-label="Rent icon" />;
            case ListingType.SWAP:
              return (
                <Handshake className="w-4 h-4 mr-2" aria-label="Swap icon" />
              );
          }
        })()}
        <span>{listing.type}</span>
      </Badge>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Mobile-only price and actions card */}
      <Card className="block lg:hidden">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl font-bold">
                {listing.name}
              </CardTitle>
              <CardDescription className="mt-1 text-gray-500">
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" /> Listed{' '}
                  {formatDate(listing.createdAt?.toString())}
                </span>
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {priceInfoComponent()}
          {actionComponent()}
        </CardContent>
      </Card>

      {/* Main content with responsive layout */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Left Column: Images & Description */}
        <div className="space-y-8 lg:col-span-2">
          <ItemMainImage
            mainImage={mainImage}
            listing={listing}
            handleClickImage={handleClickImage}
          />
          <ItemThumbnails
            images={listing.images}
            mainImage={mainImage}
            handleClickImage={handleClickImage}
            listing={listing}
          />
          <ItemDescription
            description={listing.description}
            conditionSummary={conditionSummary}
          />
          <ItemInformation
            listing={listing}
            getConditionColor={getConditionColor}
            getConditionLabel={getConditionLabel}
            edgeConfig={edgeConfig}
          />
          <ItemShippingPolicies
            deliveryOptions={deliveryOptions}
            safetyRequirements={safetyRequirements}
          />
          <div className="pt-2">
            <ItemRelatedListings
              listing={listing}
              relatedListings={relatedListings}
            />
          </div>
        </div>

        {/* Right Column: Listing Info & Seller - hidden on mobile */}
        <div className="hidden space-y-6 lg:block">
          {/* Listing Info Card */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl font-bold">
                    {listing.name}
                  </CardTitle>
                  <CardDescription className="mt-1 text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" /> Listed{' '}
                      {formatDate(listing.createdAt?.toString())}
                    </span>
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {priceInfoComponent()}
              {actionComponent()}
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              {/* Rental Availability - Only show for rental listings */}
              {listing.type === ListingType.RENT && (
                <div className="w-full">
                  <h3 className="mb-2 text-sm font-medium text-gray-500">
                    Availability
                  </h3>
                  {listing.rentalAvailability &&
                  listing.rentalAvailability.length > 0 ? (
                    <div className="p-2 border rounded-md bg-gray-50">
                      <div className="flex items-center text-sm">
                        <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                        <div>
                          <span className="font-medium">
                            Available for rent:
                          </span>{' '}
                          {formatRentalDate(
                            listing.rentalAvailability[0].startDate
                          )}{' '}
                          -{' '}
                          {formatRentalDate(
                            listing.rentalAvailability[0].endDate
                          )}
                        </div>
                      </div>
                      <div className="mt-1 text-xs text-gray-500">
                        <span className="font-medium">Rental unit:</span>{' '}
                        {listing.rentalUnit?.toLowerCase() || 'day'}{' '}
                        {listing.rentalDuration && (
                          <>
                            ({listing.rentalDuration}{' '}
                            {listing.rentalUnit?.toLowerCase() || 'day'}s total)
                          </>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No specific availability information provided. Contact
                      seller for details.
                    </p>
                  )}
                </div>
              )}

              {/* Payment Options */}
              <div className="w-full">
                <h3 className="mb-2 text-sm font-medium text-gray-500">
                  Payment Options
                </h3>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="outline"
                    className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-50"
                  >
                    <Wallet className="w-3.5 h-3.5 mr-1" />
                    Cash (Pay onsite)
                  </Badge>
                </div>
              </div>
            </CardFooter>
          </Card>

          {/* Seller Info Card */}
          {seller && (
            <InformationSeller
              seller={seller}
              listings={userListings}
              feedback={sellerFeedback}
            />
          )}

          {/* Safety Notice */}
          <InformationSafety />
        </div>
      </div>

      {/* Mobile-only Seller and Safety info */}
      <div className="block space-y-6 lg:hidden">
        {seller && (
          <InformationSeller
            seller={seller}
            listings={userListings}
            feedback={sellerFeedback}
          />
        )}
        <InformationSafety />
      </div>

      {/* Booking Dialog */}
      <ItemBookingDialog
        listing={listing}
        seller={seller}
        isOpen={isBookingOpen}
        onOpenChange={setIsBookingOpen}
        initialStartDate={
          listing.rentalAvailability && listing.rentalAvailability.length > 0
            ? moment.parseZone(listing.rentalAvailability[0].startDate).toDate()
            : undefined
        }
        initialEndDate={
          listing.rentalAvailability && listing.rentalAvailability.length > 0
            ? moment.parseZone(listing.rentalAvailability[0].endDate).toDate()
            : undefined
        }
      />

      {/* Contact Seller Form */}
      <ItemContactSeller
        seller={seller}
        listingId={listing.listingId}
        listingName={listing.name}
        isOpen={isContactFormOpen}
        onOpenChange={setIsContactFormOpen}
      />
    </div>
  );
}
