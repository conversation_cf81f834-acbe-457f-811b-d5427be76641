'use client';

import { addDays, format } from 'date-fns';
import { AlertCircle, Calendar, CheckCircle, Loader } from 'lucide-react';
import moment from 'moment';
import { useSession } from 'next-auth/react';
import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';

import { cn } from '@/lib/utils';

import {
  BookingStatus,
  ListingRentalUnit,
  PaymentStatus,
  ReturnStatus,
  type Listing,
  type Profile,
} from '@package/db/core/models';

import { createBooking } from '../../../../_actions/booking.action';

interface BookingFormData {
  paymentMethod: 'cash' | 'bank_transfer';
}

interface ItemBookingDialogProps {
  listing: Listing;
  seller: Profile | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  initialStartDate?: Date;
  initialEndDate?: Date;
}

export function ItemBookingDialog({
  listing,
  seller,
  isOpen,
  onOpenChange,
  initialStartDate,
  initialEndDate,
}: ItemBookingDialogProps) {
  const { data: session } = useSession();

  const [isCreatingBooking, setStartCreatingBookingTransition] =
    useTransition();
  const [bookingStartDate, setBookingStartDate] = useState<Date>(
    initialStartDate || moment().add(1, 'days').toDate()
  );
  const [bookingEndDate, setBookingEndDate] = useState<Date>(
    initialEndDate || moment().add(2, 'days').toDate()
  );
  const [bookingSuccess, setBookingSuccess] = useState<string | null>(null);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [calculatedPrice, setCalculatedPrice] = useState<number>(listing.price);

  const form = useForm<BookingFormData>({
    defaultValues: {
      paymentMethod: 'cash',
    },
  });

  const calculateRentalPrice = (start: Date, end: Date) => {
    if (!listing.rentalUnit) return listing.price;

    const diffTime = Math.abs(end.getTime() - start.getTime());
    let duration = 1;

    switch (listing.rentalUnit) {
      case ListingRentalUnit.HOUR:
        duration = Math.ceil(diffTime / (1000 * 60 * 60));
        break;
      case ListingRentalUnit.DAY:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        break;
      case ListingRentalUnit.WEEK:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
        break;
      case ListingRentalUnit.MONTH:
        duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30));
        break;
      default:
        duration = 1;
    }

    return listing.price * duration;
  };

  // Update price calculation when dates change
  const updateCalculatedPrice = (start: Date, end: Date) => {
    setCalculatedPrice(calculateRentalPrice(start, end));
  };

  const handleBookingSubmit = async () => {
    setBookingSuccess(null);
    setBookingError(null);

    setStartCreatingBookingTransition(async () => {
      try {
        const bookingData = {
          listingId: listing.listingId,
          userId: listing.userId,
          renterId: session?.user?.id as string,
          startDate: bookingStartDate.toISOString(),
          endDate: bookingEndDate.toISOString(),
          rentalDuration: Math.ceil(
            Math.abs(bookingEndDate.getTime() - bookingStartDate.getTime()) /
              (1000 * 60 * 60 * 24)
          ),
          status: BookingStatus.PENDING,
          paymentStatus: PaymentStatus.PENDING,
          returnStatus: ReturnStatus.PENDING,
        };

        if (!session) {
          setBookingError(
            'You must be logged in to create a booking. Please log in and try again.'
          );
          return;
        }

        if (!seller) {
          setBookingError(
            'Seller information is not available. Please try again later.'
          );
          return;
        }

        const result = await createBooking(bookingData, listing, seller);

        if (!result) {
          setBookingError(
            'An error occurred while creating your booking. Please try again.'
          );
          return;
        }

        setBookingSuccess(
          'Booking request submitted successfully! The seller will contact you soon.'
        );

        setTimeout(() => {
          onOpenChange(false);
        }, 3000);
      } catch (error) {
        setBookingError(
          'An error occurred while creating your booking. Please try again.'
        );
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Book {listing.name}</DialogTitle>
          <DialogDescription>
            {listing.rentalUnit && (
              <span className="block mt-2">
                Price: ${listing.price} per {listing.rentalUnit.toLowerCase()}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleBookingSubmit)}
            className="space-y-6"
          >
            {/* Date Selection */}
            <div className="grid gap-4 py-4 sm:grid-cols-2">
              <div className="space-y-2">
                <FormLabel>Start Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !bookingStartDate && 'text-muted-foreground'
                      )}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      {bookingStartDate
                        ? format(bookingStartDate, 'PPP')
                        : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={bookingStartDate}
                      onSelect={(date) => {
                        if (date) {
                          setBookingStartDate(date);
                          if (date > bookingEndDate) {
                            const newEndDate = addDays(date, 1);
                            setBookingEndDate(newEndDate);
                            updateCalculatedPrice(date, newEndDate);
                          } else {
                            updateCalculatedPrice(date, bookingEndDate);
                          }
                        }
                      }}
                      disabled={(date) =>
                        moment(date).isBefore(moment(), 'minute')
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <FormLabel>End Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !bookingEndDate && 'text-muted-foreground'
                      )}
                    >
                      <Calendar className="w-4 h-4 mr-2" />
                      {bookingEndDate
                        ? format(bookingEndDate, 'PPP')
                        : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={bookingEndDate}
                      onSelect={(date) => {
                        if (date && date > bookingStartDate) {
                          setBookingEndDate(date);
                          updateCalculatedPrice(bookingStartDate, date);
                        }
                      }}
                      disabled={(date) => {
                        // Disable dates before or equal to start date
                        if (
                          moment(date).isSameOrBefore(
                            moment(bookingStartDate),
                            'minute'
                          )
                        ) {
                          return true;
                        }

                        // Disable dates after the listing's rental availability end date
                        if (
                          listing.rentalAvailability &&
                          listing.rentalAvailability.length > 0
                        ) {
                          const availabilityEndDate = moment.parseZone(
                            listing.rentalAvailability[0].endDate
                          );
                          if (
                            moment(date).isAfter(availabilityEndDate, 'minute')
                          ) {
                            return true;
                          }
                        }

                        return false;
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Payment Method</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="cash" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Cash (Pay onsite)
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Price Summary */}
            <div className="p-4 mt-4 border rounded-lg bg-gray-50">
              <div className="flex justify-between">
                <span>Rental Price:</span>
                <span>
                  ${listing.price.toFixed(2)} per{' '}
                  {listing.rentalUnit?.toLowerCase()}
                </span>
              </div>
              <div className="flex justify-between mt-2">
                <span>Duration:</span>
                <span>
                  {moment(bookingStartDate).format('MMM D')} -{' '}
                  {moment(bookingEndDate).format('MMM D')}
                </span>
              </div>
              <Separator className="my-2" />
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>${calculatedPrice.toFixed(2)}</span>
              </div>
            </div>

            {(bookingSuccess || bookingError) && (
              <div
                className={`p-4 mb-4 rounded-md ${
                  bookingSuccess
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                }`}
              >
                {bookingSuccess && (
                  <div className="flex items-center text-green-800">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    <span>{bookingSuccess}</span>
                  </div>
                )}

                {bookingError && (
                  <div className="flex items-center text-red-800">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    <span>{bookingError}</span>
                  </div>
                )}
              </div>
            )}
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isCreatingBooking}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreatingBooking || !session}>
                {isCreatingBooking ? (
                  <>
                    <Loader className="w-4 h-4 mr-2 animate-spin" />
                    Booking...
                  </>
                ) : (
                  'Book Now'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
