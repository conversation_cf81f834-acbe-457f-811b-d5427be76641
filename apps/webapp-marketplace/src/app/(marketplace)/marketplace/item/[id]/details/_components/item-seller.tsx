'use client';

import { Clock, MapPin, Star, Store, UserCircle } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

import {
  Feedback,
  FeedbackStatus,
  FeedbackType,
  Listing,
  Profile,
} from '@package/db/core/models';
import { submitFeedback } from '../../../../_actions';

interface SellerInformationProps {
  seller: Profile;
  listings: Listing[];
  feedback: Feedback[];
}

export default function SellerInformation({
  seller,
  listings,
  feedback,
}: SellerInformationProps) {
  const { data: session } = useSession();

  const [userFeedback, setUserFeedback] = useState(
    feedback.find((fb) => fb.userId === session?.user?.id) || null
  );
  const [rating, setRating] = useState(userFeedback?.rating || 0);
  const [comment, setComment] = useState(userFeedback?.comment || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [feedbackStatus, setFeedbackStatus] = useState<
    'success' | 'error' | null
  >(null);

  const handleFeedbackSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (seller.id === session?.user?.id) {
      setFeedbackStatus('error');
      return;
    }

    if (userFeedback) {
      setFeedbackStatus('error');
      return;
    }

    setIsSubmitting(true);

    try {
      const newFeedback = await submitFeedback({
        profileOrStoreId: seller.id,
        userId: session?.user?.id as string,
        rating,
        comment,
        type: FeedbackType.FEEDBACK,
        status: FeedbackStatus.PUBLISHED,
      });

      setUserFeedback(newFeedback);
      setFeedbackStatus('success');
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setFeedbackStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Seller Information</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Feedback submission status */}
        {feedbackStatus === 'success' && (
          <Alert variant="default" className="mb-4">
            <AlertTitle>Feedback Submitted</AlertTitle>
            <AlertDescription>
              Your feedback has been submitted successfully.
            </AlertDescription>
          </Alert>
        )}
        {feedbackStatus === 'error' && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Submission Failed</AlertTitle>
            <AlertDescription>
              There was an issue submitting your feedback. Please try again
              later.
            </AlertDescription>
          </Alert>
        )}

        {/* Existing seller information */}
        <div className="flex items-center mb-4">
          <Avatar className="w-12 h-12 mr-4">
            <AvatarImage src={seller.image} alt={seller.name} />
            <AvatarFallback>
              <UserCircle className="w-6 h-6" />
            </AvatarFallback>
          </Avatar>
          <div>
            <Link
              href={`/marketplace/store/${seller.id}/listings`}
              className="text-lg font-medium hover:underline"
            >
              {seller.name}
            </Link>
            <div className="flex items-center mt-1 text-sm text-muted-foreground">
              <Star className="w-4 h-4 mr-1 text-yellow-500 fill-yellow-500" />
              <span>
                {(
                  feedback.reduce((sum, fb) => sum + (fb.rating || 0), 0) /
                    feedback.length || 0
                ).toFixed(2)}{' '}
                ({feedback.length} reviews)
              </span>
            </div>
          </div>
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            <span>
              {seller.verificationInfo?.address?.city &&
              seller.verificationInfo?.address?.country
                ? `${seller.verificationInfo.address.city}, ${seller.verificationInfo.address.country}`
                : seller.address?.city && seller.address?.country
                ? `${seller.address.city}, ${seller.address.country}`
                : 'Location not specified'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span>
              Member since{' '}
              {new Date(seller.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
              })}
            </span>
          </div>
        </div>

        <Separator className="my-4" />

        {/* Feedback form */}
        {session && !userFeedback && (
          <form onSubmit={handleFeedbackSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium">Rating</label>
              <div className="flex items-center gap-1 mt-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    type="button"
                    key={star}
                    onClick={() => setRating(star)}
                    className={`w-6 h-6 ${
                      star <= rating ? 'text-yellow-500' : 'text-gray-300'
                    }`}
                  >
                    ★
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium">Comment</label>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="w-full p-2 border rounded-md"
                rows={3}
                required
              />
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-white bg-blue-500 rounded-md disabled:opacity-50"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </button>
          </form>
        )}

        {userFeedback && (
          <div className="mt-4">
            <h4 className="text-sm font-medium">Your Feedback</h4>
            <div className="flex items-center gap-1 mt-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`w-4 h-4 ${
                    star <= (userFeedback.rating ?? 0)
                      ? 'text-yellow-500'
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <p className="mt-2 text-sm">{userFeedback.comment}</p>
          </div>
        )}

        <Separator className="my-4" />

        {/* Feedback section */}
        {feedback.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium">Recent Feedback</h3>
            <div className="space-y-4">
              {feedback.slice(0, 3).map((fb) => (
                <div
                  key={fb.feedbackId}
                  className="pb-2 border-b last:border-b-0"
                >
                  <div className="flex items-center gap-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-3 h-3 ${
                          star <= (fb.rating || 0)
                            ? 'text-yellow-500 fill-yellow-500'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-xs text-muted-foreground">
                      {fb.createdAt
                        ? new Date(Number(fb.createdAt)).toLocaleDateString()
                        : ''}
                    </span>
                  </div>
                  {fb.comment && (
                    <div className="mt-1 text-sm">{fb.comment}</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        <Separator className="my-4" />

        {/* More from this seller */}
        <div>
          <h3 className="mb-3 text-sm font-medium">More from this seller</h3>
          <div className="space-y-3">
            {listings?.slice(0, 3).map((item) => (
              <Link
                key={item.listingId}
                href={`/marketplace/item/${item.listingId}/details`}
                className="flex items-center justify-between p-2 transition-colors rounded-md hover:bg-accent"
              >
                <span className="text-sm truncate max-w-[70%]">
                  {item.name}
                </span>
                <span className="text-sm font-medium">${item.price}</span>
              </Link>
            ))}

            <Link
              href={`/marketplace/store/${seller.id}/listings`}
              className="flex items-center mt-2 text-sm text-primary"
            >
              <Store className="w-4 h-4 mr-1" /> View all listings
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
