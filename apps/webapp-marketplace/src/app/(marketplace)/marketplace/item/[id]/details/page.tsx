import { Section } from '../../../../../_components/section';
import {
  getFeedbackByProfileOrStoreId,
  getListingByCategoryId,
  getListingById,
  getListingsByUserId,
  getProfileById,
  getUserById,
} from '../../../_actions';

import ItemDetails from './_components/item-details';

import { edgeConfig } from '@package/configs';

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function Page({ params }: PageProps) {
  const id = (await params).id;
  const listing = await getListingById(id);
  const userListings = await getListingsByUserId(listing.userId);

  // Try to get Profile data first, fallback to basic User data if needed
  let seller = await getProfileById(listing.userId);
  if (!seller) {
    console.warn(
      `Profile not found for user ${listing.userId}, falling back to basic user data`
    );
    const basicUser = await getUserById(listing.userId);
    // Convert User to Profile-like structure for compatibility
    seller = basicUser
      ? ({
          ...basicUser,
          createdAt: basicUser.createdAt?.toString() || '',
          type: 'USER',
          role: basicUser.role || 'USER',
          password: '', // Not needed for display
        } as any)
      : null;
  }

  const relatedListings = (await getListingByCategoryId(listing.categoryId))
    .sort((a, b) => {
      return (
        new Date(b.createdAt?.toString() || '').getTime() -
        new Date(a.createdAt?.toString() || '').getTime()
      );
    })
    .slice(0, 3);
  const sellerFeedback = await getFeedbackByProfileOrStoreId(listing.userId);

  return (
    <>
      <Section>
        {listing && (
          <ItemDetails
            listing={listing}
            userListings={userListings}
            seller={seller}
            relatedListings={relatedListings}
            enableContactSeller={
              edgeConfig.featureFlags.marketplace.enableContactSeller
            }
            enableAddToCart={false}
            sellerFeedback={sellerFeedback}
          />
        )}
      </Section>
    </>
  );
}
