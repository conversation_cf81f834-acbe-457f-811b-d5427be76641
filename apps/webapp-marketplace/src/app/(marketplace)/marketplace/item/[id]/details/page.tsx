import { Section } from '../../../../../_components/section';
import {
  getFeedbackByProfileOrStoreId,
  getListingByCategoryId,
  getListingById,
  getListingsByUserId,
  getProfileById,
} from '../../../_actions';

import ItemDetails from './_components/item-details';

import { edgeConfig } from '@package/configs';

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function Page({ params }: PageProps) {
  const id = (await params).id;
  const listing = await getListingById(id);
  const userListings = await getListingsByUserId(listing.userId);
  const seller = await getProfileById(listing.userId);
  const relatedListings = (await getListingByCategoryId(listing.categoryId))
    .sort((a, b) => {
      return (
        new Date(b.createdAt?.toString() || '').getTime() -
        new Date(a.createdAt?.toString() || '').getTime()
      );
    })
    .slice(0, 3);
  const sellerFeedback = await getFeedbackByProfileOrStoreId(listing.userId);

  return (
    <>
      <Section>
        {listing && (
          <ItemDetails
            listing={listing}
            userListings={userListings}
            seller={seller}
            relatedListings={relatedListings}
            enableContactSeller={
              edgeConfig.featureFlags.marketplace.enableContactSeller
            }
            enableAddToCart={false}
            sellerFeedback={sellerFeedback}
          />
        )}
      </Section>
    </>
  );
}
