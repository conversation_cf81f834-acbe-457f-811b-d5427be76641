'use server';

import { backendConfig } from '@package/configs';
import { getAccountService, getUserService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';

export async function getUserByEmail(email: string) {
  const service = getUserService('dynamodb', getDynamoDBClient());
  const user = await service.getUserByEmail(email);

  return user;
}

export async function getUserById(userId: string) {
  const service = getUserService('dynamodb', getDynamoDBClient());
  const user = await service.getUserById(userId);

  return user;
}

/**
 * Get user profile with extended fields (including verification info and address)
 */
export async function getProfileById(userId: string) {
  try {
    const config = backendConfig.aws;
    const service = getAccountService(
      'dynamodb',
      config.dynamodb.tables.account,
      getDynamoDBClient()
    );
    const profile = await service.getProfileById(userId);

    return profile;
  } catch (error) {
    console.error('Error getting profile by ID:', error);
    return null;
  }
}
