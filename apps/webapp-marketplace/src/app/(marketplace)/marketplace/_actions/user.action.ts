'use server';

import { backendConfig } from '@package/configs';
import { getUserService, getAccountService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';

export async function getUserByEmail(email: string) {
  const service = getUserService('dynamodb', getDynamoDBClient());
  const user = await service.getUserByEmail(email);

  return user;
}

export async function getUserById(userId: string) {
  const service = getUserService('dynamodb', getDynamoDBClient());
  const user = await service.getUserById(userId);

  return user;
}

/**
 * Get user profile with extended fields (including verification info and address)
 */
export async function getProfileById(userId: string) {
  const config = backendConfig.aws;
  const service = getAccountService(
    'dynamodb',
    config.dynamodb.tables.core,
    getDynamoDBClient()
  );
  const profile = await service.getProfileById(userId);

  return profile;
}
