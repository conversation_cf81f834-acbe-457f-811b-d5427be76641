'use server';

import { backendConfig, edgeConfig } from '@package/configs';
import { getBookingService } from '@package/db';
import { getDynamoDBClient } from '@package/db/adapters/dynamodb/client';
import type {
  Booking,
  CreateBookingInput,
  Listing,
  Profile,
} from '@package/db/core/models';
import { mailerService } from '@package/mailer';

// TODO check for existing booking for the same user and listing
// TODO check for date conflict with existing bookings

export async function createBooking(
  booking: CreateBookingInput,
  listing: Listing,
  receiver: Profile
): Promise<Booking | null> {
  try {
    const config = backendConfig.aws;
    const service = getBookingService(
      'dynamodb',
      config.dynamodb.tables.core,
      getDynamoDBClient()
    );

    const subject = 'Booking Awaiting Approval';
    const emailContent = `
Hello,<br /><br />

You've received a new booking request for your listing: <b>${listing.name}</b><br /><br />

Please check your bookings for more details: <br />
<a href="${edgeConfig.baseUrl}${edgeConfig.routes.dashboard.bookings}">${edgeConfig.baseUrl}${edgeConfig.routes.dashboard.bookings}</a>

<br /><br />
Thank you,<br />
Circular Marketplace Team
`;

    const newBooking = await service.createBooking(booking);

    const emailResult = await mailerService().sendMessage(
      receiver.email,
      subject,
      emailContent
    );

    if (!emailResult) {
      console.error('Error sending email:', emailResult);
      return null;
    }

    return newBooking;
  } catch (error) {
    console.error('Error creating booking:', JSON.stringify(error));
    return null;
  }
}
