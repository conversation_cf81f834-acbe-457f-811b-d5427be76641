#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to populate GSI fields for existing listings
 * This will update all existing listings to include the GSI partition and sort keys
 */

import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { backendConfig } from '@package/configs';
import { createListingRepository } from '../src/adapters/dynamodb/listing.repository';
import { ListingStatus } from '../src/adapters/dynamodb/electrodb/models/listing.model';

// Create DynamoDB client
const getDynamoDBClient = (): DynamoDBDocumentClient => {
  const config = backendConfig.aws;
  
  const client = new DynamoDBClient({
    region: config.region,
    endpoint: config.endpoint,
    credentials: config.credentials,
  });

  return DynamoDBDocumentClient.from(client);
};

async function populateGSIFields() {
  console.log('🚀 Starting GSI field population...');
  
  const client = getDynamoDBClient();
  const tableName = backendConfig.aws.dynamodb.tables.core;
  const repository = createListingRepository(client, tableName);

  try {
    // First, let's scan all listings to see what we have
    console.log('📊 Scanning existing listings...');
    
    // We'll use the raw DynamoDB client to scan all items
    const scanParams = {
      TableName: tableName,
      FilterExpression: 'begins_with(pk, :listingPrefix)',
      ExpressionAttributeValues: {
        ':listingPrefix': 'Listing#'
      }
    };

    let items: any[] = [];
    let lastEvaluatedKey: any = undefined;
    let totalScanned = 0;

    do {
      const scanResult = await client.scan({
        ...scanParams,
        ExclusiveStartKey: lastEvaluatedKey
      });

      if (scanResult.Items) {
        items = items.concat(scanResult.Items);
        totalScanned += scanResult.Items.length;
        console.log(`📄 Scanned ${totalScanned} items so far...`);
      }

      lastEvaluatedKey = scanResult.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    console.log(`✅ Found ${items.length} total listings`);

    // Filter for items that need GSI fields
    const listingsNeedingUpdate = items.filter(item => 
      !item.gsi2pk || !item.gsi2sk || !item.gsi3pk || !item.gsi3sk
    );

    console.log(`🔧 ${listingsNeedingUpdate.length} listings need GSI field updates`);

    if (listingsNeedingUpdate.length === 0) {
      console.log('🎉 All listings already have GSI fields populated!');
      return;
    }

    // Update each listing to populate GSI fields
    let updated = 0;
    for (const item of listingsNeedingUpdate) {
      try {
        const updateParams = {
          TableName: tableName,
          Key: {
            pk: item.pk,
            sk: item.sk
          },
          UpdateExpression: 'SET gsi2pk = :gsi2pk, gsi2sk = :gsi2sk, gsi3pk = :gsi3pk, gsi3sk = :gsi3sk',
          ExpressionAttributeValues: {
            ':gsi2pk': item.status || ListingStatus.ACTIVE,
            ':gsi2sk': item.createdAt || new Date().toISOString(),
            ':gsi3pk': `${item.categoryId}#${item.status || ListingStatus.ACTIVE}`,
            ':gsi3sk': item.createdAt || new Date().toISOString()
          }
        };

        await client.update(updateParams);
        updated++;
        
        if (updated % 10 === 0) {
          console.log(`📝 Updated ${updated}/${listingsNeedingUpdate.length} listings...`);
        }
      } catch (error) {
        console.error(`❌ Failed to update listing ${item.pk}:`, error);
      }
    }

    console.log(`✅ Successfully updated ${updated} listings with GSI fields`);
    console.log('🎉 GSI population complete!');
    
    // Verify the update
    console.log('🔍 Verifying GSI population...');
    const verifyParams = {
      TableName: tableName,
      IndexName: 'gsi2pk-gsi2sk-index',
      KeyConditionExpression: 'gsi2pk = :status',
      ExpressionAttributeValues: {
        ':status': ListingStatus.ACTIVE
      },
      Limit: 5
    };

    const verifyResult = await client.query(verifyParams);
    console.log(`✅ GSI verification: Found ${verifyResult.Items?.length || 0} active listings in GSI`);

  } catch (error) {
    console.error('❌ Error during GSI population:', error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  populateGSIFields()
    .then(() => {
      console.log('🎉 Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { populateGSIFields };
