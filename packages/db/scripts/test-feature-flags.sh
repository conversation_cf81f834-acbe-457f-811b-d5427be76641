#!/bin/bash

# DynamoDB GSI Feature Flag Testing Script
# This script helps you test different feature flag configurations

echo "🧪 DynamoDB GSI Feature Flag Testing"
echo "===================================="

# Function to set environment variables and test
test_configuration() {
    local gsi_flag=$1
    local fallback_flag=$2
    local description=$3
    
    echo ""
    echo "📋 Testing: $description"
    echo "   USE_DYNAMODB_GSI=$gsi_flag"
    echo "   FALLBACK_TO_SCAN_ON_GSI_ERROR=$fallback_flag"
    echo ""
    
    # Export the environment variables
    export USE_DYNAMODB_GSI=$gsi_flag
    export FALLBACK_TO_SCAN_ON_GSI_ERROR=$fallback_flag
    
    # You can add your test commands here
    echo "   Environment variables set. Run your tests now."
    echo "   Press Enter to continue to next configuration..."
    read -r
}

echo "This script will help you test different GSI feature flag configurations."
echo "Make sure your application is running and ready to test."
echo ""

# Test Configuration 1: Current state (GSI with fallback)
test_configuration "true" "true" "GSI Enabled with Scan Fallback (Current/Recommended)"

# Test Configuration 2: Force scan only
test_configuration "false" "true" "Force Scan Only (Testing scan performance)"

# Test Configuration 3: GSI only (no fallback)
test_configuration "true" "false" "GSI Only - No Fallback (After migration)"

# Test Configuration 4: Both disabled (edge case)
test_configuration "false" "false" "Both Disabled (Edge case - should use scan)"

echo ""
echo "✅ Feature flag testing complete!"
echo ""
echo "💡 Tips:"
echo "   - Monitor your application logs during each test"
echo "   - Check DynamoDB costs in AWS console"
echo "   - Measure response times for each configuration"
echo "   - Test with different query types (list, search, category)"
echo ""
echo "📊 Expected Log Messages:"
echo "   GSI Enabled: No special logs (queries succeed)"
echo "   GSI Disabled: 'GSI disabled by feature flag, using scan'"
echo "   GSI Fallback: 'GSI not available, falling back to scan'"
echo "   GSI Error: 'GSI failed and fallback disabled'"
echo ""
echo "🎯 Recommended Production Config:"
echo "   USE_DYNAMODB_GSI=true"
echo "   FALLBACK_TO_SCAN_ON_GSI_ERROR=true"
