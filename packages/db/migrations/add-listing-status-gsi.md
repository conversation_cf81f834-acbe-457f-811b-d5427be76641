# DynamoDB GSI Migration: Add Listing Status Index

## Overview
This migration adds a new Global Secondary Index (GSI) to efficiently query active listings by creation date, eliminating the need for expensive table scans.

## New GSI Configuration

### Index Name: `gsi2pk-gsi2sk-index`

**Partition Key (gsi2pk):** `status` (String)
**Sort Key (gsi2sk):** `createdAt` (String)

## AWS CLI Command

```bash
aws dynamodb update-table \
  --table-name YOUR_TABLE_NAME \
  --attribute-definitions \
    AttributeName=gsi2pk,AttributeType=S \
    AttributeName=gsi2sk,AttributeType=S \
  --global-secondary-index-updates \
    '[{
      "Create": {
        "IndexName": "gsi2pk-gsi2sk-index",
        "KeySchema": [
          {
            "AttributeName": "gsi2pk",
            "KeyType": "HASH"
          },
          {
            "AttributeName": "gsi2sk",
            "KeyType": "RANGE"
          }
        ],
        "Projection": {
          "ProjectionType": "ALL"
        },
        "ProvisionedThroughput": {
          "ReadCapacityUnits": 5,
          "WriteCapacityUnits": 5
        }
      }
    }]'
```

## CloudFormation Template

```yaml
Resources:
  YourDynamoDBTable:
    Type: AWS::DynamoDB::Table
    Properties:
      # ... existing properties
      GlobalSecondaryIndexes:
        # ... existing GSIs
        - IndexName: gsi2pk-gsi2sk-index
          KeySchema:
            - AttributeName: gsi2pk
              KeyType: HASH
            - AttributeName: gsi2sk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
          ProvisionedThroughput:
            ReadCapacityUnits: 5
            WriteCapacityUnits: 5
      AttributeDefinitions:
        # ... existing attributes
        - AttributeName: gsi2pk
          AttributeType: S
        - AttributeName: gsi2sk
          AttributeType: S
```

## CDK (TypeScript)

```typescript
// Add to your DynamoDB table construct
table.addGlobalSecondaryIndex({
  indexName: 'gsi2pk-gsi2sk-index',
  partitionKey: {
    name: 'gsi2pk',
    type: dynamodb.AttributeType.STRING,
  },
  sortKey: {
    name: 'gsi2sk',
    type: dynamodb.AttributeType.STRING,
  },
  projectionType: dynamodb.ProjectionType.ALL,
  readCapacity: 5,
  writeCapacity: 5,
});
```

## Cost Savings

### Before (Table Scan)
- **Operation:** Scan entire table
- **Cost:** ~$0.25 per 1M items scanned
- **Performance:** Slow, gets worse as table grows
- **Capacity:** Consumes read capacity for entire table

### After (GSI Query)
- **Operation:** Query specific status partition
- **Cost:** ~$0.25 per 1M items returned (not scanned)
- **Performance:** Fast, consistent regardless of table size
- **Capacity:** Only consumes capacity for returned items

### Example Savings
For a table with 100K listings where you want 12 active listings:
- **Scan:** Reads 100K items, costs ~$0.025
- **Query:** Reads 12 items, costs ~$0.000003
- **Savings:** ~99.99% cost reduction

## Verification

After creating the GSI, verify it's working:

```bash
# Check GSI status
aws dynamodb describe-table --table-name YOUR_TABLE_NAME

# Test query
aws dynamodb query \
  --table-name YOUR_TABLE_NAME \
  --index-name gsi2pk-gsi2sk-index \
  --key-condition-expression "gsi2pk = :status" \
  --expression-attribute-values '{":status":{"S":"ACTIVE"}}' \
  --scan-index-forward false \
  --limit 12
```

## Notes

1. **GSI Creation Time:** Can take several minutes depending on table size
2. **Backfill:** DynamoDB will automatically backfill existing items
3. **Capacity:** Monitor and adjust GSI capacity based on usage
4. **Cost:** GSI has separate read/write capacity charges
