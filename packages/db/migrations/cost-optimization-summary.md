# DynamoDB Cost Optimization Summary

## Problem
The original implementation used DynamoDB `scan` operations which are expensive and inefficient:
- Scans read every item in the table
- Costs scale with table size, not result size
- Performance degrades as table grows
- High read capacity consumption

## Solution
Implemented efficient GSI (Global Secondary Index) queries:

### 1. Status-Based GSI (`gsi2pk-gsi2sk-index`)
- **Partition Key:** `status` (e.g., "ACTIVE")
- **Sort Key:** `createdAt` (for chronological ordering)
- **Use Case:** List all active listings by creation date

### 2. Category-Based GSI (`gsi3pk-gsi3sk-index`)
- **Partition Key:** `categoryId#status` (e.g., "electronics#ACTIVE")
- **Sort Key:** `createdAt` (for chronological ordering)
- **Use Case:** List active listings in a specific category

## Cost Comparison

### Before (Scan Operations)
```typescript
// This scans the ENTIRE table
const result = await model.scan
  .where((attr, op) => op.eq(attr.status, 'ACTIVE'))
  .go();
```

**Cost Example (100K total items, 10K active):**
- Reads: 100K items
- Cost: ~$0.025 per query
- Time: 2-5 seconds

### After (GSI Queries)
```typescript
// This queries only the ACTIVE partition
const result = await model.query
  .byStatus({ status: 'ACTIVE' })
  .go({ limit: 12, order: 'desc' });
```

**Cost Example (same dataset):**
- Reads: 12 items
- Cost: ~$0.000003 per query
- Time: 50-100ms

## Savings Calculation

| Metric | Before (Scan) | After (Query) | Savings |
|--------|---------------|---------------|---------|
| Items Read | 100,000 | 12 | 99.99% |
| Cost per Query | $0.025 | $0.000003 | 99.99% |
| Response Time | 2-5s | 50-100ms | 95%+ |
| Read Capacity | High | Minimal | 99%+ |

## Functions Optimized

### 1. `listListings()`
- **Before:** Table scan with client-side sorting
- **After:** GSI query with DynamoDB sorting
- **Savings:** 99%+ cost reduction

### 2. `getListingByCategoryId()`
- **Before:** Table scan with category filter
- **After:** Category GSI query
- **Savings:** 90%+ cost reduction (depends on category size)

### 3. `searchListings()`
- **Before:** Always used table scan
- **After:** Uses GSI when possible, falls back to scan for complex filters
- **Savings:** 99%+ for simple searches, varies for complex ones

## Implementation Status

✅ **Completed:**
- Added GSI definitions to ElectroDB model
- Updated repository functions to use GSI queries
- Maintained backward compatibility
- Added intelligent query routing

⚠️ **Required:**
- Create GSIs in your DynamoDB table (see migration guide)
- Monitor GSI capacity and adjust as needed

## Migration Steps

1. **Create GSIs** (see `add-listing-status-gsi.md`)
2. **Wait for backfill** (DynamoDB will populate existing data)
3. **Deploy code changes**
4. **Monitor performance and costs**

## Monitoring

After migration, monitor:
- GSI read/write capacity utilization
- Query response times
- Cost reduction in AWS billing
- Error rates (should be minimal)

## Future Optimizations

Consider adding more GSIs for:
- User-specific queries (`userId#status`)
- Type-based queries (`type#status`)
- Price range queries (requires careful design)

## Notes

- GSIs have their own read/write capacity
- Each GSI doubles storage costs (but saves query costs)
- ROI is positive when query frequency > storage cost
- For this use case, savings are immediate and substantial
