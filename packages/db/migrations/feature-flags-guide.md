# DynamoDB GSI Feature Flags Guide

## Overview
The DynamoDB repository now supports feature flags to control GSI usage and fallback behavior. This gives you complete control over when to use expensive scans vs efficient GSI queries.

## Feature Flags

### 1. `USE_DYNAMODB_GSI`
**Controls whether to attempt GSI queries**

- **Default:** `true` (attempts GSI queries)
- **Values:** `"true"` | `"false"`
- **Environment Variable:** `USE_DYNAMODB_GSI`

### 2. `FALLBACK_TO_SCAN_ON_GSI_ERROR`
**Controls whether to fallback to scan when GSI fails**

- **Default:** `true` (falls back to scan)
- **Values:** `"true"` | `"false"`
- **Environment Variable:** `FALLBACK_TO_SCAN_ON_GSI_ERROR`

## Configuration Scenarios

### Scenario 1: GSI Not Created Yet (Current State)
```bash
# .env
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

**Behavior:**
- ✅ Tries GSI queries first
- ✅ Falls back to scan when GSI doesn't exist
- ✅ Application works normally
- 📝 Logs: "GSI not available, falling back to scan"

### Scenario 2: Force Scan Only (Testing/Debugging)
```bash
# .env
USE_DYNAMODB_GSI=false
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

**Behavior:**
- ❌ Skips GSI queries entirely
- ✅ Uses scan operations directly
- ✅ Useful for testing scan performance
- 📝 Logs: "GSI disabled by feature flag, using scan"

### Scenario 3: GSI Only (After Migration)
```bash
# .env
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=false
```

**Behavior:**
- ✅ Uses GSI queries
- ❌ Throws error if GSI fails (no fallback)
- ⚠️ Application fails if GSI has issues
- 📝 Logs: "GSI failed and fallback disabled"

### Scenario 4: Production Ready (Recommended)
```bash
# .env
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

**Behavior:**
- ✅ Uses GSI queries for optimal performance
- ✅ Falls back to scan if GSI has issues
- ✅ Maximum reliability and performance
- 📝 Logs: Minimal (only on GSI failures)

## Migration Timeline

### Phase 1: Current (GSI Creation)
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```
- Search works with scan fallback
- Create GSIs in background
- Monitor logs for GSI availability

### Phase 2: Testing (GSI Active)
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```
- GSI queries succeed
- No more fallback logs
- Monitor performance improvements

### Phase 3: Confidence (Optional)
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=false
```
- Pure GSI mode
- Fail fast if GSI issues
- Maximum cost savings

### Phase 4: Production (Recommended)
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```
- Best of both worlds
- Performance + reliability
- Handles edge cases gracefully

## Monitoring & Logs

### Log Messages to Watch For

#### GSI Working (Good)
```
# No special logs - queries succeed silently
```

#### GSI Disabled (Expected)
```
[listListings] GSI disabled by feature flag, using scan
[getListingByCategoryId] GSI disabled by feature flag, using scan
```

#### GSI Fallback (Temporary)
```
[listListings] GSI not available, falling back to scan
[searchListings-category] Category GSI not available, falling back to scan
```

#### GSI Error (Needs Attention)
```
[listListings] GSI failed and fallback disabled: <error details>
```

## Environment Variable Examples

### Docker Compose
```yaml
services:
  app:
    environment:
      - USE_DYNAMODB_GSI=true
      - FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

### AWS Amplify
```bash
# In Amplify Console Environment Variables
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

### Local Development
```bash
# .env.local
USE_DYNAMODB_GSI=false  # Test scan performance
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
```

## Testing Scenarios

### Test Scan Performance
```bash
USE_DYNAMODB_GSI=false
# Forces all queries to use scan
```

### Test GSI Performance
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=false
# Fails if GSI not available
```

### Test Fallback Logic
```bash
USE_DYNAMODB_GSI=true
FALLBACK_TO_SCAN_ON_GSI_ERROR=true
# Create GSI, then delete it to test fallback
```

## Troubleshooting

### Search Returns Empty Results
1. Check if GSI exists: `aws dynamodb describe-table --table-name YOUR_TABLE`
2. Check feature flags: Ensure `USE_DYNAMODB_GSI=true`
3. Check logs for error messages

### High DynamoDB Costs
1. Set `USE_DYNAMODB_GSI=false` temporarily
2. Create GSIs following migration guide
3. Set `USE_DYNAMODB_GSI=true` after GSI creation

### Application Errors
1. Set `FALLBACK_TO_SCAN_ON_GSI_ERROR=true`
2. Check GSI status and capacity
3. Monitor CloudWatch metrics

## Best Practices

1. **Always enable fallback in production** (`FALLBACK_TO_SCAN_ON_GSI_ERROR=true`)
2. **Monitor logs** to understand when fallbacks occur
3. **Test both modes** in staging environment
4. **Gradually disable fallback** only after GSI stability is confirmed
5. **Keep feature flags** for future debugging and flexibility

## Cost Impact by Configuration

| Configuration | Cost | Performance | Reliability |
|---------------|------|-------------|-------------|
| GSI Only | Lowest | Fastest | Medium |
| Scan Only | Highest | Slowest | High |
| GSI + Fallback | Low | Fast | Highest |

**Recommendation:** Use GSI + Fallback for production workloads.
