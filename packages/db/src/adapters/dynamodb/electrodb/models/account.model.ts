import type { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

import { Entity } from 'electrodb';
import { v4 as uuid } from 'uuid';

export enum UserRole {
  USER = 'USER',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
  STORE_OWNER = 'STORE_OWNER',
}

interface ProfileModelParams {
  client: DynamoDBDocumentClient;
  table: string;
}

export const CreateProfileModel = ({ client, table }: ProfileModelParams) => {
  return new Entity(
    {
      model: {
        entity: 'Profile',
        version: '1',
        service: 'AccountService',
      },
      attributes: {
        id: {
          type: 'string',
          required: true,
          default: () => uuid(),
        },
        email: {
          type: 'string',
          required: true,
          validate: (value) => /\S+@\S+\.\S+/.test(value),
        },
        emailVerified: {
          type: 'string',
          required: false,
        },
        name: {
          type: 'string',
          required: true,
        },
        password: {
          type: 'string',
          required: true,
        },
        image: {
          type: 'string',
          required: false,
        },
        resetToken: {
          type: 'string',
          required: false,
        },
        resetTokenExpiration: {
          type: 'string',
          required: false,
        },
        role: {
          type: 'string',
          required: true,
          default: UserRole.USER,
          validate: (value) =>
            Object.values(UserRole).includes(value as UserRole),
        },
        createdAt: {
          type: 'string',
          required: true,
          default: () => new Date().toISOString(),
        },
        // Auth.js specific fields
        type: {
          type: 'string',
          required: true,
          default: 'USER',
        },
        // Extended profile fields
        address: {
          type: 'map',
          properties: {
            street: { type: 'string' },
            city: { type: 'string' },
            state: { type: 'string' },
            zipCode: { type: 'string' },
            country: { type: 'string' },
          },
        },
        phone: {
          type: 'string',
        },
        bio: {
          type: 'string',
          validate: (value) => value.length <= 500,
        },
        verificationStatus: {
          type: 'string',
          validate: (value) =>
            ['pending', 'verified', 'rejected'].includes(value),
        },
        verificationDocuments: {
          type: 'list',
          items: {
            type: 'map',
            properties: {
              type: { type: 'string', required: true },
              url: { type: 'string', required: true },
              status: {
                type: 'string',
                required: true,
                validate: (value) =>
                  ['pending', 'approved', 'rejected'].includes(value),
              },
              uploadedAt: { type: 'string', required: true },
            },
          },
        },
        verificationInfo: {
          type: 'map',
          properties: {
            phone: { type: 'string' },
            dateOfBirth: { type: 'string' },
            address: {
              type: 'map',
              properties: {
                street: { type: 'string' },
                city: { type: 'string' },
                state: { type: 'string' },
                zipCode: { type: 'string' },
                country: { type: 'string' },
              },
            },
            nameVerified: { type: 'boolean' },
            contactInfoVerified: { type: 'boolean' },
            addressVerified: { type: 'boolean' },
          },
        },
      },
      indexes: {
        primary: {
          pk: {
            field: 'pk',
            composite: ['id'],
            template: 'USER#${id}',
            casing: 'none',
          },
          sk: {
            field: 'sk',
            composite: ['id'],
            template: 'USER#${id}',
            casing: 'none',
          },
        },
        email: {
          index: 'gsi1pk-gsi1sk-index',
          pk: {
            field: 'gsi1pk',
            composite: ['email'],
            template: 'USER#${email}',
            casing: 'none',
          },
          sk: {
            field: 'gsi1sk',
            composite: ['email'],
            template: 'USER#${email}',
            casing: 'none',
          },
        },
      },
    },
    {
      client,
      table,
    }
  );
};

// Create Account model for OAuth providers
export const CreateAccountModel = ({ client, table }: ProfileModelParams) => {
  return new Entity(
    {
      model: {
        entity: 'Account',
        version: '1',
        service: 'AccountService',
      },
      attributes: {
        id: {
          type: 'string',
          required: true,
          default: () => uuid(),
        },
        userId: {
          type: 'string',
          required: true,
        },
        type: {
          type: 'string',
          required: true,
        },
        provider: {
          type: 'string',
          required: true,
        },
        providerAccountId: {
          type: 'string',
          required: true,
        },
        refresh_token: {
          type: 'string',
        },
        access_token: {
          type: 'string',
        },
        expires_at: {
          type: 'number',
        },
        token_type: {
          type: 'string',
        },
        scope: {
          type: 'string',
        },
        id_token: {
          type: 'string',
        },
        session_state: {
          type: 'string',
        },
      },
      indexes: {
        primary: {
          pk: {
            field: 'pk',
            composite: ['userId'],
            template: 'USER#${userId}',
          },
          sk: {
            field: 'sk',
            composite: ['provider', 'providerAccountId'],
            template: 'ACCOUNT#${provider}#${providerAccountId}',
          },
        },
        byProvider: {
          index: 'gsi1pk-gsi1sk-index',
          pk: {
            field: 'gsi1pk',
            composite: ['provider'],
            template: 'ACCOUNT#${provider}',
          },
          sk: {
            field: 'gsi1sk',
            composite: ['providerAccountId'],
            template: 'ACCOUNT#${providerAccountId}',
          },
        },
      },
    },
    {
      client,
      table,
    }
  );
};

// Create Session model
export const CreateSessionModel = ({ client, table }: ProfileModelParams) => {
  return new Entity(
    {
      model: {
        entity: 'Session',
        version: '1',
        service: 'AccountService',
      },
      attributes: {
        id: {
          type: 'string',
          required: true,
          default: () => uuid(),
        },
        sessionToken: {
          type: 'string',
          required: true,
        },
        userId: {
          type: 'string',
          required: true,
        },
        expires: {
          type: 'number',
          required: true,
        },
        type: {
          type: 'string',
          required: true,
          default: 'SESSION',
        },
      },
      indexes: {
        primary: {
          pk: {
            field: 'pk',
            composite: ['userId'],
            template: 'USER#${userId}',
          },
          sk: {
            field: 'sk',
            composite: ['sessionToken'],
            template: 'SESSION#${sessionToken}',
          },
        },
        bySession: {
          index: 'gsi1pk-gsi1sk-index',
          pk: {
            field: 'gsi1pk',
            composite: ['sessionToken'],
            template: 'SESSION#${sessionToken}',
          },
          sk: {
            field: 'gsi1sk',
            composite: ['sessionToken'],
            template: 'SESSION#${sessionToken}',
          },
        },
      },
    },
    {
      client,
      table,
    }
  );
};

// Create VerificationToken model
export const CreateVerificationTokenModel = ({
  client,
  table,
}: ProfileModelParams) => {
  return new Entity(
    {
      model: {
        entity: 'VerificationToken',
        version: '1',
        service: 'AccountService',
      },
      attributes: {
        identifier: {
          type: 'string',
          required: true,
        },
        token: {
          type: 'string',
          required: true,
        },
        expires: {
          type: 'number',
          required: true,
        },
        type: {
          type: 'string',
          required: true,
          default: 'VT',
        },
      },
      indexes: {
        primary: {
          pk: {
            field: 'pk',
            composite: ['identifier'],
            template: 'VT#${identifier}',
          },
          sk: {
            field: 'sk',
            composite: ['token'],
            template: 'VT#${token}',
          },
        },
      },
    },
    {
      client,
      table,
    }
  );
};
