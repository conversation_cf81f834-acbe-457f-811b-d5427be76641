import type { CreateEntityItem, EntityItem, QueryResponse } from 'electrodb';
import { Entity } from 'electrodb';
import { v4 as uuid } from 'uuid';

import type { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

import {
  calculateDateDifferenceInDays,
  getTodayInUTCFormat,
} from '@package/common-utils';

export enum ListingStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  SOLD = 'SOLD',
  EXPIRED = 'EXPIRED',
  DELETED = 'DELETED',
  BOOKED = 'BOOKED',
}

export enum ListingType {
  SELL = 'SELL',
  FREE = 'FREE',
  RENT = 'RENT',
  SWAP = 'SWAP',
}

export enum ListingCondition {
  NEW = 'NEW',
  LIKE_NEW = 'LIKE_NEW',
  GOOD = 'GOOD',
  FAIR = 'FAIR',
  POOR = 'POOR',
}

export enum ListingRentalUnit {
  HOUR = 'HOUR',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
}

interface ListingModelParams {
  client: DynamoDBDocumentClient;
  tableName: string;
}

export const CreateListingModel = ({
  client,
  tableName,
}: ListingModelParams) => {
  return new Entity(
    {
      model: {
        entity: 'Listing',
        version: '1',
        service: 'CoreService',
      },
      attributes: {
        listingId: {
          type: 'string',
          required: true,
          default: () => uuid(),
        },
        name: {
          type: 'string',
          required: true,
        },
        description: {
          type: 'string',
          required: true,
        },
        price: {
          type: 'number',
          required: true,
        },
        categoryId: {
          type: 'string',
          required: true,
        },
        userId: {
          type: 'string',
          required: true,
        },
        status: {
          type: 'string',
          required: true,
          validate: (value) =>
            Object.values(ListingStatus).includes(value as ListingStatus),
        },
        images: {
          type: 'list',
          required: true,
          items: {
            type: 'map',
            properties: {
              url: {
                type: 'string',
                required: true,
              },
              isMain: {
                type: 'boolean',
                required: true,
                default: false,
              },
            },
          },
        },
        type: {
          type: 'string',
          required: true,
          validate: (value) =>
            Object.values(ListingType).includes(value as ListingType),
        },
        condition: {
          type: 'string',
          required: true,
          validate: (value) =>
            Object.values(ListingCondition).includes(value as ListingCondition),
        },
        attributes: {
          type: 'list',
          items: {
            type: 'map',
            properties: {
              key: {
                type: 'string',
                required: true,
              },
              value: {
                type: 'string',
                required: true,
              },
            },
          },
        },
        rentalUnit: {
          type: 'string',
          validate: (value) =>
            value === undefined ||
            Object.values(ListingRentalUnit).includes(
              value as ListingRentalUnit
            ),
        },
        rentalAvailability: {
          type: 'list',
          items: {
            type: 'map',
            properties: {
              startDate: {
                type: 'string',
                required: true,
              },
              endDate: {
                type: 'string',
                required: true,
              },
            },
          },
        },
        rentalDuration: {
          type: 'number',
          watch: ['rentalAvailability'],
          get: (_, { rentalAvailability }) => {
            if (!rentalAvailability || rentalAvailability.length === 0) {
              return undefined;
            }

            const startDate = new Date(rentalAvailability[0].startDate);
            const endDate = new Date(rentalAvailability[0].endDate);

            if (!startDate || !endDate) {
              return undefined;
            }

            return calculateDateDifferenceInDays(startDate, endDate);
          },
          set: () => undefined,
        },
        createdAt: {
          type: 'string',
          required: true,
          default: () => getTodayInUTCFormat(),
        },
        updatedAt: {
          type: 'string',
        },
      },
      indexes: {
        primary: {
          pk: {
            field: 'pk',
            composite: ['listingId'],
          },
          sk: {
            field: 'sk',
            composite: [],
          },
        },
        user: {
          index: 'gsi1pk-gsi1sk-index',
          pk: {
            field: 'gsi1pk',
            composite: ['userId'],
          },
          sk: {
            field: 'gsi1sk',
            composite: ['createdAt'], // TODO: improve sorting, this might be wrong index
          },
        },
        byStatus: {
          index: 'gsi2pk-gsi2sk-index',
          pk: {
            field: 'gsi2pk',
            composite: ['status'],
          },
          sk: {
            field: 'gsi2sk',
            composite: ['createdAt'],
          },
        },
        byCategory: {
          index: 'gsi3pk-gsi3sk-index',
          pk: {
            field: 'gsi3pk',
            composite: ['categoryId', 'status'],
          },
          sk: {
            field: 'gsi3sk',
            composite: ['createdAt'],
          },
        },
      },
    },
    { client, table: tableName }
  );
};

export type ListingEntity = ReturnType<typeof CreateListingModel>;
export type ListingItem = EntityItem<ListingEntity>;
export type CreateListingItem = CreateEntityItem<ListingEntity>;
export type QueryListingResponse = QueryResponse<ListingEntity>;
