import type { DynamoDBAdapterOptions } from '@auth/dynamodb-adapter';
import type { DynamoDBDocument } from '@aws-sdk/lib-dynamodb';

import type { User } from '../../core/models/user.model';
import type { UserRepository } from '../../core/repositories/user.repository';
import { createAuthJsDynamoDBAuthAdapter } from '../dynamodb/authjs/authjs';

import { backendConfig } from '@package/configs';

/**
 * This repository is used to interact with the user data in DynamoDB for Auth.js
 * Note: changes must be related to the Auth.js adapter, not the ElectroDB model
 */
export const createUserRepository = (
  client: DynamoDBDocument,
  options?: DynamoDBAdapterOptions
): UserRepository => {
  const _options = {
    ...options,
    tableName: backendConfig.aws.dynamodb.tables.auth.tableName,
    partitionKey: backendConfig.aws.dynamodb.tables.auth.partitionKey,
    sortKey: backendConfig.aws.dynamodb.tables.auth.sortKey,
    indexName: backendConfig.aws.dynamodb.tables.auth.indexName,
    indexPartitionKey: backendConfig.aws.dynamodb.tables.auth.indexPartitionKey,
    indexSortKey: backendConfig.aws.dynamodb.tables.auth.indexSortKey,
  };

  const adapter = createAuthJsDynamoDBAuthAdapter(client, _options);
  return {
    getAdapter: () => adapter,
    async getUserById(id: string): Promise<User> {
      if (adapter?.getUser) {
        return adapter.getUser(id) as User;
      }
      return Promise.reject(new Error('Adapter not found'));
    },
    async createUser(user: User): Promise<User> {
      if (adapter?.createUser) {
        return adapter?.createUser(user) as User;
      }
      return Promise.reject(new Error('Adapter not found'));
    },
    async getUserByEmail(email: string): Promise<User> {
      if (adapter?.getUserByEmail) {
        return adapter?.getUserByEmail(email) as User;
      }
      return Promise.reject(new Error('Adapter not found'));
    },
    async updateUser(user: User): Promise<User> {
      if (adapter?.updateUser) {
        return adapter?.updateUser(user) as User;
      }
      return Promise.reject(new Error('Adapter not found'));
    },
  };
};
