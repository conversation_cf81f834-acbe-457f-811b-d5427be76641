import type { DynamoDBDocument } from '@aws-sdk/lib-dynamodb';

import type {
  CreateListingInput,
  Listing,
  UpdateListingInput,
  UpdateListingStatusInput,
} from '../../core/models';

import type { ListingRepository } from '../../core/repositories';

import {
  type CreateListingItem,
  CreateListingModel,
  ListingStatus,
} from './electrodb/models/listing.model';

export const createListingRepository = (
  client: DynamoDBDocument,
  tableName: string
): ListingRepository => {
  const model = CreateListingModel({
    client,
    tableName,
  });

  return {
    async createListing(listing: CreateListingInput): Promise<Listing> {
      const _listing = {
        ...listing,
        images: listing.images.map((image) => ({
          url: image.url,
          isMain: image.isMain === undefined ? false : image.isMain,
        })),
      };

      const result = await model
        .put(_listing as unknown as CreateListingItem)
        .go();

      return result.data as unknown as Listing;
    },
    async listListings(limit?: number): Promise<Listing[]> {
      // Use GSI to query active listings sorted by creation date (most efficient)
      const result = await model.query
        .byStatus({ status: ListingStatus.ACTIVE as string })
        .go({
          order: 'desc', // Latest first
          limit: limit || 50, // Default limit to prevent large queries
        });

      return (result.data || []) as unknown as Listing[];
    },
    async getListingById(listingId: string): Promise<Listing> {
      const result = await model.get({ listingId }).go();
      return (result.data || []) as unknown as Listing;
    },
    async getListingByCategoryId(categoryId: string): Promise<Listing[]> {
      // Use the category GSI for efficient querying
      const result = await model.query
        .byCategory({
          categoryId,
          status: ListingStatus.ACTIVE as string,
        })
        .go({
          order: 'desc', // Latest first
        });
      return (result.data || []) as unknown as Listing[];
    },
    async getListingsByUserId(userId: string): Promise<Listing[]> {
      // TODO - avoid scanning the entire table
      const result = await model.scan
        .where((attr, op) => {
          return op.eq(attr.userId, userId);
        })
        .go();
      return (result.data || []) as unknown as Listing[];
    },
    async updateListingById(
      listingId: string,
      listing: UpdateListingInput
    ): Promise<Listing> {
      const _listing = {
        ...listing,
        images: listing.images.map((image) => ({
          url: image.url,
          isMain: image.isMain === undefined ? false : image.isMain,
        })),
      };

      const result = await model
        .update({ listingId })
        .set(_listing as unknown as UpdateListingInput)
        .go();

      return result.data as unknown as Listing;
    },
    async deleteListingById(listingId: string): Promise<null> {
      await model.delete({ listingId }).go();
      return null;
    },
    async searchListings(
      query?: string,
      categoryId?: string,
      type?: string,
      condition?: string,
      minPrice?: number,
      maxPrice?: number,
      limit?: number,
      lastKey?: Record<string, unknown>
    ): Promise<Listing[]> {
      // TODO: implement pagination with lastKey
      //       Promise<{ items: Listing[]; lastEvaluatedKey?: Record<string, any> }>
      //        return {
      //          items: (result.data || []) as unknown as Listing[],
      //          lastEvaluatedKey: result.cursor?.lastEvaluatedKey
      //        };

      // If only filtering by category, use the category GSI
      if (
        categoryId &&
        !query &&
        !type &&
        !condition &&
        !minPrice &&
        !maxPrice
      ) {
        const result = await model.query
          .byCategory({
            categoryId,
            status: ListingStatus.ACTIVE as string,
          })
          .go({
            order: 'desc', // Latest first
            limit: limit || 50,
          });
        return (result.data || []) as unknown as Listing[];
      }

      // If no filters except status, use the efficient status GSI query
      if (
        !query &&
        !categoryId &&
        !type &&
        !condition &&
        !minPrice &&
        !maxPrice
      ) {
        const result = await model.query
          .byStatus({ status: ListingStatus.ACTIVE as string })
          .go({
            order: 'desc', // Latest first
            limit: limit || 50,
          });
        return (result.data || []) as unknown as Listing[];
      }

      // For complex searches, fall back to scan (but this should be optimized with better indexes)
      let scanOperation = model.scan;

      try {
        if (categoryId) {
          scanOperation = scanOperation.where((attr, op) =>
            op.eq(attr.categoryId, categoryId)
          );
        }

        if (type) {
          scanOperation = scanOperation.where((attr, op) =>
            op.eq(attr.type, type)
          );
        }

        if (condition) {
          scanOperation = scanOperation.where((attr, op) =>
            op.eq(attr.condition, condition)
          );
        }

        if (minPrice !== undefined) {
          scanOperation = scanOperation.where((attr, op) =>
            op.gte(attr.price, minPrice)
          );
        }

        if (maxPrice !== undefined) {
          scanOperation = scanOperation.where((attr, op) =>
            op.lte(attr.price, maxPrice)
          );
        }

        if (query?.trim()) {
          const searchQuery = query.trim();

          scanOperation = scanOperation.where(
            (attr, op) =>
              `${op.contains(attr.name, searchQuery)} OR ${op.contains(
                attr.description,
                searchQuery
              )}`
          );
        }

        // Always add status filter for active listings
        scanOperation = scanOperation.where((attr, op) =>
          op.eq(attr.status, ListingStatus.ACTIVE as string)
        );

        // TODO: Implement pagination with lastKey
        const LIMIT_MAX = limit ? limit : 1000;

        // Execute the scan operation
        const result = await scanOperation.go({ limit: LIMIT_MAX });

        // Sort results by updatedAt in descending order (latest first)
        // TODO: DynamoDB does not support sorting by updatedAt, so we do it here
        const sortedResults = [...(result.data || [])].sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt).getTime();
          const dateB = new Date(b.updatedAt || b.createdAt).getTime();
          return dateB - dateA; // Descending order
        });

        return sortedResults as unknown as Listing[];
      } catch (error) {
        console.error('Error in searchListings:', error);
        throw error;
      }
    },
    async updateListingStatusById(
      listingId: string,
      status: UpdateListingStatusInput
    ): Promise<Listing> {
      const result = await model
        .update({ listingId })
        .set({ status: status.status as string })
        .go();
      return result.data as unknown as Listing;
    },
  };
};
