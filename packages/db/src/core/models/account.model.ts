import type { UserRole } from '../../adapters/dynamodb/electrodb/models/account.model';

export { UserRole as Role } from '../../adapters/dynamodb/electrodb/models/account.model';

export interface Profile {
  id: string;
  email: string;
  emailVerified?: string;
  name: string;
  password: string;
  image?: string;
  resetToken?: string;
  resetTokenExpiration?: string;
  role: UserRole;
  createdAt: string;
  type: string;
  // Extended fields for enhanced profile
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  phone?: string;
  bio?: string;
  verificationStatus?: 'pending' | 'verified' | 'rejected';
  verificationDocuments?: Array<{
    type: string;
    url: string;
    status: 'pending' | 'approved' | 'rejected';
    uploadedAt: string;
  }>;
}

export type UpdateProfileInput = {
  emailVerified?: string;
  name?: string;
  password?: string;
  image?: string;
  resetToken?: string;
  resetTokenExpiration?: string;
  role?: UserRole;
  // Extended fields
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  phone?: string;
  bio?: string;
  verificationStatus?: 'pending' | 'verified' | 'rejected';
  verificationDocuments?: Array<{
    type: string;
    url: string;
    status: 'pending' | 'approved' | 'rejected';
    uploadedAt: string;
  }>;
};

// Account interface for OAuth providers
export interface Account {
  id: string;
  userId: string;
  type: string;
  provider: string;
  providerAccountId: string;
  refresh_token?: string;
  access_token?: string;
  expires_at?: number;
  token_type?: string;
  scope?: string;
  id_token?: string;
  session_state?: string;
}

// Session interface
export interface Session {
  id: string;
  sessionToken: string;
  userId: string;
  expires: number;
  type: string;
}

// VerificationToken interface
export interface VerificationToken {
  identifier: string;
  token: string;
  expires: number;
  type: string;
}
