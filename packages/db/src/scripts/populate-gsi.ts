/**
 * GSI Population Script - Run within NX monorepo
 * Usage: Create a simple API endpoint or add to an existing script
 */

import { DynamoDBDocumentClient, ScanCommand, UpdateCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';

interface PopulateGSIConfig {
  region: string;
  endpoint?: string;
  accessKeyId: string;
  secretAccessKey: string;
  tableName: string;
}

const ListingStatus = {
  ACTIVE: 'ACTIVE',
  DRAFT: 'DRAFT',
  SOLD: 'SOLD',
  EXPIRED: 'EXPIRED',
  DELETED: 'DELETED',
} as const;

export async function populateGSIFields(config: PopulateGSIConfig) {
  console.log('🚀 Starting GSI field population...');
  
  // Create DynamoDB client
  const client = new DynamoDBClient({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
    },
  });

  const docClient = DynamoDBDocumentClient.from(client);

  try {
    // Scan all listings
    console.log('📊 Scanning existing listings...');
    
    const scanParams = {
      TableName: config.tableName,
      FilterExpression: 'begins_with(pk, :listingPrefix)',
      ExpressionAttributeValues: {
        ':listingPrefix': 'Listing#'
      }
    };

    let items: any[] = [];
    let lastEvaluatedKey: any = undefined;
    let totalScanned = 0;

    do {
      const command = new ScanCommand({
        ...scanParams,
        ExclusiveStartKey: lastEvaluatedKey
      });

      const scanResult = await docClient.send(command);

      if (scanResult.Items) {
        items = items.concat(scanResult.Items);
        totalScanned += scanResult.Items.length;
        console.log(`📄 Scanned ${totalScanned} items so far...`);
      }

      lastEvaluatedKey = scanResult.LastEvaluatedKey;
    } while (lastEvaluatedKey);

    console.log(`✅ Found ${items.length} total listings`);

    // Filter for items that need GSI fields
    const listingsNeedingUpdate = items.filter(item => 
      !item.gsi2pk || !item.gsi2sk || !item.gsi3pk || !item.gsi3sk
    );

    console.log(`🔧 ${listingsNeedingUpdate.length} listings need GSI field updates`);

    if (listingsNeedingUpdate.length === 0) {
      console.log('🎉 All listings already have GSI fields populated!');
      return { success: true, updated: 0, total: items.length };
    }

    // Update each listing
    let updated = 0;
    const errors: any[] = [];

    for (const item of listingsNeedingUpdate) {
      try {
        const updateCommand = new UpdateCommand({
          TableName: config.tableName,
          Key: {
            pk: item.pk,
            sk: item.sk
          },
          UpdateExpression: 'SET gsi2pk = :gsi2pk, gsi2sk = :gsi2sk, gsi3pk = :gsi3pk, gsi3sk = :gsi3sk',
          ExpressionAttributeValues: {
            ':gsi2pk': item.status || ListingStatus.ACTIVE,
            ':gsi2sk': item.createdAt || new Date().toISOString(),
            ':gsi3pk': `${item.categoryId}#${item.status || ListingStatus.ACTIVE}`,
            ':gsi3sk': item.createdAt || new Date().toISOString()
          }
        });

        await docClient.send(updateCommand);
        updated++;
        
        if (updated % 10 === 0) {
          console.log(`📝 Updated ${updated}/${listingsNeedingUpdate.length} listings...`);
        }
      } catch (error) {
        console.error(`❌ Failed to update listing ${item.pk}:`, error);
        errors.push({ item: item.pk, error });
      }
    }

    console.log(`✅ Successfully updated ${updated} listings with GSI fields`);
    
    // Verify the update
    console.log('🔍 Verifying GSI population...');
    try {
      const verifyCommand = new QueryCommand({
        TableName: config.tableName,
        IndexName: 'gsi2pk-gsi2sk-index',
        KeyConditionExpression: 'gsi2pk = :status',
        ExpressionAttributeValues: {
          ':status': ListingStatus.ACTIVE
        },
        Limit: 5
      });

      const verifyResult = await docClient.send(verifyCommand);
      console.log(`✅ GSI verification: Found ${verifyResult.Items?.length || 0} active listings in GSI`);
    } catch (verifyError) {
      console.warn('⚠️ GSI verification failed (this is normal if GSI is still building):', verifyError);
    }

    console.log('🎉 GSI population complete!');
    
    return { 
      success: true, 
      updated, 
      total: items.length, 
      errors: errors.length,
      errorDetails: errors 
    };

  } catch (error) {
    console.error('❌ Error during GSI population:', error);
    throw error;
  }
}
