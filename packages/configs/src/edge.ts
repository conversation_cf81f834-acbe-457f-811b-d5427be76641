import { PUBLIC_ENV } from './env';

export const edgeConfig = {
  baseUrl: PUBLIC_ENV.BASE_URL,
  port: PUBLIC_ENV.PORT,
  environment: PUBLIC_ENV.NODE_ENV,

  routes: {
    home: '/',
    marketing: {
      about: '/about/story',
      team: '/about/team',
      faq: '/info/faq',
      shipping: '/info/shipping-and-returns',
      terms: '/legal/terms-of-service',
      privacy: '/legal/privacy-policy',
      cookies: '/legal/cookie-policy',
    },
    auth: {
      signin: '/signin',
      signup: '/signup',
      'reset-password': '/reset-password',
      'change-password': '/change-password',
      'verify-email': '/verify-email',
    },
    marketplace: {
      root: '/marketplace',
    },
    dashboard: {
      messages: '/dashboard/messages',
      bookings: '/dashboard/bookings',
    },
  },

  featureFlags: {
    auth: {
      enablePasswordReset: true,
    },
    marketplace: {
      enableDeliveryOptions: false,
      enableItemConditionSummary: false,
      enableItemConditionSafetyRequirements: false,
      enableContactSeller: true,
      reviews: {
        enableSellerReviews: false,
      },
      enableItemAttributes: false,
    },
    dashboard: {
      enableMessaging: false,
      enableNotifications: false,
      enableProfileActivity: false,
      enableProfileSettings: false,
      enableBookingsBeforeAfterConditions: false,
    },
    profile: {
      enableStats: false,
      enableAbout: false,
      enableMessageProfile: false,
      enableReview: true,
    },
    database: {
      // DynamoDB optimization flags
      useDynamoDBGSI: false, // Temporarily disable GSI to force scan operations
      fallbackToScanOnGSIError: true, // Fallback to scan if GSI fails
    },
  },
};
