import { ENV } from './env';

/**
 * Backend configuration
 * For server components and API routes
 */
export const backendConfig = {
  aws: {
    endpoint: ENV.AWS_ENDPOINT,
    region: ENV.AWS_REGION as string,
    credentials: {
      accessKeyId: ENV.AWS_ACCESS_KEY_ID as string,
      secretAccessKey: ENV.AWS_SECRET_ACCESS_KEY as string,
    },
    dynamodb: {
      tables: {
        core: ENV.AWS_DYNAMODB_TABLE_CORE as string,
        auth: {
          tableName: ENV.AWS_DYNAMODB_TABLE_AUTH as string,
          partitionKey: ENV.AWS_DYNAMODB_TABLE_AUTH_PARTITION_KEY,
          sortKey: ENV.AWS_DYNAMODB_TABLE_AUTH_SORT_KEY,
          indexName: ENV.AWS_DYNAMODB_TABLE_AUTH_INDEX_NAME,
          indexPartitionKey: ENV.AWS_DYNAMODB_TABLE_AUTH_INDEX_PARTITION_KEY,
          indexSortKey: ENV.AWS_DYNAMODB_TABLE_AUTH_INDEX_SORT_KEY,
        },
        account: ENV.AWS_DYNAMODB_TABLE_ACCOUNT as string,
      },
    },
    s3: {
      bucket: ENV.AWS_S3_BUCKET as string,
      region: ENV.AWS_S3_BUCKET_REGION as string,
      credentials: {
        accessKeyId: ENV.AWS_S3_ACCESS_KEY_ID as string,
        secretAccessKey: ENV.AWS_S3_SECRET_ACCESS_KEY as string,
      },
    },
    bedrock: {
      modelId:
        ENV.AWS_BEDROCK_MODEL_ID || 'anthropic.claude-3-haiku-********-v1:0',
      maxTokens: 1000,
      temperature: 0.7,
    },
  },
  sendgrid: {
    apiKey: ENV.SENDGRID_API_KEY as string,
    fromEmail: ENV.SENDGRID_FROM_EMAIL as string,
    fromName: ENV.SENDGRID_FROM_NAME as string,
  },
};
