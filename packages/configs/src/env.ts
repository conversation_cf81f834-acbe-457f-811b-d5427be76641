/* eslint-disable */
// @ts-nocheck

// Check if running in AWS Amplify environment
// Amplify sets environment variables like AMPLIFY_MONOREPO_APP_ROOT or AMPLIFY_APP_ID
const isAmplifyEnvironment = !!(process.env.CLOUD_PROVIDER === 'AMPLIFY');

const isRunningInAWS = (): boolean => {
  // AWS Amplify
  if (process.env['AWS_APP_ID'] || process.env['AMPLIFY_APP_ID']) return true;

  // AWS Lambda
  if (process.env['AWS_LAMBDA_FUNCTION_NAME']) return true;

  // AWS ECS
  if (process.env['AWS_CONTAINER_CREDENTIALS_RELATIVE_URI']) return true;

  // EC2 (check if we're on an EC2 instance via metadata presence)
  // This is a simplified check - in real implementation you might want
  // to actually query the metadata endpoint
  if (process.env['AWS_EXECUTION_ENV']) return true;

  return false;
};

// Public env variables (safe to expose to client)
export const PUBLIC_ENV = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  PORT: process.env.NEXT_PUBLIC_PORT || 3000,
};

// Map AWS Amplify environment variables to our expected names
// AWS Amplify doesn't allow AWS_ prefixed env variables
function getEnvValue(amplifyName: string, awsName: string, defaultValue = '') {
  if (isAmplifyEnvironment) {
    return process.env[amplifyName] || defaultValue;
  }
  return process.env[amplifyName] || defaultValue;
}

// Safe environment variables that work in both Node.js and Edge Runtime
export const ENV = {
  // Client-safe variables
  ...PUBLIC_ENV,

  isRunningInAWS: isRunningInAWS(),

  // These will be undefined in client components, but available in server components
  // Format: getEnvValue('AMPLIFY_FRIENDLY_NAME', 'AWS_PREFIXED_NAME', 'default')
  AWS_ENDPOINT: process.env.AMZ_ENDPOINT,
  AWS_REGION: process.env.AMZ_REGION,
  AWS_ACCESS_KEY_ID: process.env.AMZ_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AMZ_SECRET_ACCESS_KEY,
  AWS_DYNAMODB_TABLE_CORE: process.env.AMZ_DYNAMODB_TABLE_CORE,
  AWS_S3_BUCKET: process.env.AMZ_S3_BUCKET,
  AWS_S3_BUCKET_REGION: process.env.AMZ_S3_BUCKET_REGION,
  AWS_S3_ACCESS_KEY_ID: process.env.AMZ_S3_ACCESS_KEY_ID,
  AWS_S3_SECRET_ACCESS_KEY: process.env.AMZ_S3_SECRET_ACCESS_KEY,
  AWS_DYNAMODB_TABLE_AUTH: process.env.AMZ_DYNAMODB_TABLE_AUTH,
  AWS_DYNAMODB_TABLE_AUTH_PARTITION_KEY: 'pk',
  AWS_DYNAMODB_TABLE_AUTH_SORT_KEY: 'sk',
  AWS_DYNAMODB_TABLE_AUTH_INDEX_NAME: 'gsi1pk-gsi1sk-index',
  AWS_DYNAMODB_TABLE_AUTH_INDEX_PARTITION_KEY: 'gsi1pk',
  AWS_DYNAMODB_TABLE_AUTH_INDEX_SORT_KEY: 'gsi1sk',
  AWS_DYNAMODB_TABLE_ACCOUNT: process.env.AMZ_DYNAMODB_TABLE_ACCOUNT,

  // AWS Bedrock
  AWS_BEDROCK_MODEL_ID: process.env.AMZ_BEDROCK_MODEL_ID,
  AWS_BEDROCK_ANTHROPIC_VERSION: process.AMZ_BEDROCK_ANTHROPIC_VERSION,

  // SendGrid
  SENDGRID_API_KEY: process.env.SENDGRID_API_KEY,
  SENDGRID_FROM_EMAIL: process.env.SENDGRID_FROM_EMAIL,
  SENDGRID_FROM_NAME: process.env.SENDGRID_FROM_NAME,
};

// Debugging information
if (typeof window === 'undefined') {
  console.log('[CM] Environment detection:');
  console.log('[CM] - Running in AWS:', isRunningInAWS());

  console.log(
    '[CM] - Running in AWS Amplify:',
    isAmplifyEnvironment ? 'Yes' : 'No'
  );

  if (isAmplifyEnvironment) {
    console.log('[CM] - Using Amplify-compatible environment variable names');
  } else {
    console.log('[CM] - Using standard AWS_ prefixed environment variables');
  }
}
