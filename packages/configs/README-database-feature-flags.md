# Database Feature Flags in edge.ts

## Overview
Database optimization feature flags are now configured directly in `packages/configs/src/edge.ts` for easy control without environment variables.

## Current Configuration

```typescript
// packages/configs/src/edge.ts
featureFlags: {
  // ... other flags
  database: {
    // DynamoDB optimization flags
    useDynamoDBGSI: true, // Enable GSI queries for better performance
    fallbackToScanOnGSIError: true, // Fallback to scan if GSI fails
  },
}
```

## Flag Controls

### `useDynamoDBGSI`
**Controls whether to attempt GSI queries**

- **`true`** (default): Tries GSI queries first for optimal performance
- **`false`**: Skips GSI entirely, uses scan operations only

### `fallbackToScanOnGSIError`
**Controls fallback behavior when GSI queries fail**

- **`true`** (default): Falls back to scan if GSI fails (maximum reliability)
- **`false`**: Throws error if GSI fails (fail-fast mode)

## Configuration Scenarios

### 1. Current Recommended (Default)
```typescript
database: {
  useDynamoDBGSI: true,
  fallbackToScanOnGSIError: true,
}
```
- ✅ Tries GSI for performance
- ✅ Falls back to scan for reliability
- ✅ Works before and after GSI creation

### 2. Force Scan Only (Testing)
```typescript
database: {
  useDynamoDBGSI: false,
  fallbackToScanOnGSIError: true,
}
```
- ❌ Skips GSI entirely
- ✅ Uses scan operations only
- 🧪 Good for performance testing

### 3. GSI Only (After Migration)
```typescript
database: {
  useDynamoDBGSI: true,
  fallbackToScanOnGSIError: false,
}
```
- ✅ Uses GSI for maximum performance
- ❌ Fails if GSI unavailable
- 💰 Maximum cost savings

### 4. Both Disabled (Edge Case)
```typescript
database: {
  useDynamoDBGSI: false,
  fallbackToScanOnGSIError: false,
}
```
- ❌ Skips GSI
- ❌ No fallback (uses scan anyway)
- 🐛 Only for debugging

## How to Change Flags

### 1. Edit the Configuration
```bash
# Edit the file directly
vim packages/configs/src/edge.ts

# Or use your preferred editor
code packages/configs/src/edge.ts
```

### 2. Update the Values
```typescript
// Change these values as needed
database: {
  useDynamoDBGSI: false,        // ← Change this
  fallbackToScanOnGSIError: true, // ← Or this
}
```

### 3. Restart Your Application
```bash
# The changes take effect on next app restart
npm run dev
# or
npm run build && npm start
```

## Log Messages

### GSI Working (Silent)
```
# No special logs - queries succeed normally
```

### GSI Disabled
```
[listListings] GSI disabled by feature flag, using scan
[getListingByCategoryId] GSI disabled by feature flag, using scan
```

### GSI Fallback
```
[listListings] GSI not available, falling back to scan
[searchListings-category] Category GSI not available, falling back to scan
```

### GSI Error (No Fallback)
```
[listListings] GSI failed and fallback disabled: <error details>
```

## Migration Timeline

### Phase 1: Current State
```typescript
// Keep defaults - works with or without GSI
useDynamoDBGSI: true,
fallbackToScanOnGSIError: true,
```

### Phase 2: After GSI Creation
```typescript
// Optional: Disable fallback for maximum savings
useDynamoDBGSI: true,
fallbackToScanOnGSIError: false,
```

### Phase 3: Testing/Debugging
```typescript
// Force scan mode for comparison
useDynamoDBGSI: false,
fallbackToScanOnGSIError: true,
```

## Benefits

### ✅ **Easy Control**
- No environment variables needed
- Direct code changes
- Version controlled configuration

### ✅ **Immediate Effect**
- Changes apply on app restart
- No deployment pipeline changes
- Quick testing and rollback

### ✅ **Clear Intent**
- Configuration is explicit in code
- Easy to see current settings
- Self-documenting

## Quick Reference

| Need | useDynamoDBGSI | fallbackToScanOnGSIError |
|------|----------------|--------------------------|
| **Current (Recommended)** | `true` | `true` |
| **Test Scan Performance** | `false` | `true` |
| **Maximum Cost Savings** | `true` | `false` |
| **Debug GSI Issues** | `false` | `false` |

## Notes

- Changes require application restart
- Default values work for most scenarios
- Monitor logs to understand behavior
- Keep fallback enabled in production for reliability
