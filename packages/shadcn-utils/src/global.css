@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 170 95% 41%;
    --primary-foreground: 210 40% 98%;

    --secondary: 186 96% 90%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 186 96% 90%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 170 95% 41%;

    --radius: 0.3rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Circular Marketplace palette */
    --cm-teal: 150 70% 42%;
    --cm-teal-50: 150 70% 95%;
    --cm-teal-100: 150 70% 90%;
    --cm-teal-200: 150 70% 80%;
    --cm-teal-300: 150 70% 65%;
    --cm-teal-400: 150 70% 50%;
    --cm-teal-500: 150 70% 42%;
    --cm-teal-600: 150 70% 35%;
    --cm-teal-700: 150 70% 28%;
    --cm-teal-800: 150 70% 21%;
    --cm-teal-900: 150 70% 14%;
    --cm-teal-950: 150 70% 10%;

    --cm-salmon: 0 100% 70%;
    --cm-salmon-50: 0 100% 98%;
    --cm-salmon-100: 0 100% 95%;
    --cm-salmon-200: 0 100% 90%;
    --cm-salmon-300: 0 100% 85%;
    --cm-salmon-400: 0 100% 77%;
    --cm-salmon-500: 0 100% 70%;
    --cm-salmon-600: 0 100% 60%;
    --cm-salmon-700: 0 100% 50%;
    --cm-salmon-800: 0 100% 40%;
    --cm-salmon-900: 0 100% 30%;
    --cm-salmon-950: 0 100% 25%;

    --cm-lime: 74 84% 64%;
    --cm-lime-50: 74 84% 96%;
    --cm-lime-100: 74 84% 92%;
    --cm-lime-200: 74 84% 84%;
    --cm-lime-300: 74 84% 76%;
    --cm-lime-400: 74 84% 68%;
    --cm-lime-500: 74 84% 64%;
    --cm-lime-600: 74 84% 52%;
    --cm-lime-700: 74 84% 40%;
    --cm-lime-800: 74 84% 28%;
    --cm-lime-900: 74 84% 16%;
    --cm-lime-950: 74 84% 10%;

    --cm-dark: 27 35% 12%;
    --cm-dark-50: 27 35% 90%;
    --cm-dark-100: 27 35% 80%;
    --cm-dark-200: 27 35% 60%;
    --cm-dark-300: 27 35% 40%;
    --cm-dark-400: 27 35% 25%;
    --cm-dark-500: 27 35% 12%;
    --cm-dark-600: 27 35% 10%;
    --cm-dark-700: 27 35% 8%;
    --cm-dark-800: 27 35% 6%;
    --cm-dark-900: 27 35% 4%;
    --cm-dark-950: 27 35% 2%;

    --cm-light: 0 0% 94%;
    --cm-light-50: 0 0% 100%;
    --cm-light-100: 0 0% 99%;
    --cm-light-200: 0 0% 98%;
    --cm-light-300: 0 0% 97%;
    --cm-light-400: 0 0% 96%;
    --cm-light-500: 0 0% 94%;
    --cm-light-600: 0 0% 83%;
    --cm-light-700: 0 0% 72%;
    --cm-light-800: 0 0% 61%;
    --cm-light-900: 0 0% 50%;
    --cm-light-950: 0 0% 44%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 170 95% 41%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 170 95% 41%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Circular Marketplace dark mode palette adjustments if needed */
    --cm-teal: 150 60% 42%;
    --cm-salmon: 0 90% 65%;
    --cm-lime: 74 75% 55%;
    --cm-dark: 27 35% 10%;
    --cm-light: 0 0% 90%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Chrome, Safari, Edge, Opera */
input[type='search']::-webkit-search-cancel-button {
  -webkit-appearance: none;
  appearance: none;
}

/* Firefox */
input[type='search']::-moz-search-cancel-button {
  display: none;
}

/* Microsoft Edge */
input[type='search']::-ms-clear {
  display: none;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
