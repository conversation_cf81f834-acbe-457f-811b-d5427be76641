import { createGlobPatternsForDependencies } from '@nx/react/tailwind';
import { join } from 'node:path';
import TailwindAnimate from 'tailwindcss-animate';

import type { Config } from 'tailwindcss';

export function buildConfig(appDir: string): Config {
  return {
    darkMode: ['class'],
    content: [
      join(
        appDir,
        '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'
      ),
      ...createGlobPatternsForDependencies(appDir),
    ],
    theme: {
      container: {
        center: true,
        padding: '2rem',
        screens: {
          '2xl': '1400px',
        },
      },
      extend: {
        colors: {
          border: 'hsl(var(--border))',
          input: 'hsl(var(--input))',
          ring: 'hsl(var(--ring))',
          background: 'hsl(var(--background))',
          foreground: 'hsl(var(--foreground))',
          primary: {
            DEFAULT: 'hsl(var(--primary))',
            foreground: 'hsl(var(--primary-foreground))',
          },
          secondary: {
            DEFAULT: 'hsl(var(--secondary))',
            foreground: 'hsl(var(--secondary-foreground))',
          },
          destructive: {
            DEFAULT: 'hsl(var(--destructive))',
            foreground: 'hsl(var(--destructive-foreground))',
          },
          muted: {
            DEFAULT: 'hsl(var(--muted))',
            foreground: 'hsl(var(--muted-foreground))',
          },
          accent: {
            DEFAULT: 'hsl(var(--accent))',
            foreground: 'hsl(var(--accent-foreground))',
          },
          popover: {
            DEFAULT: 'hsl(var(--popover))',
            foreground: 'hsl(var(--popover-foreground))',
          },
          card: {
            DEFAULT: 'hsl(var(--card))',
            foreground: 'hsl(var(--card-foreground))',
          },
          sidebar: {
            DEFAULT: 'hsl(var(--sidebar-background))',
            foreground: 'hsl(var(--sidebar-foreground))',
            primary: 'hsl(var(--sidebar-primary))',
            'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
            accent: 'hsl(var(--sidebar-accent))',
            'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
            border: 'hsl(var(--sidebar-border))',
            ring: 'hsl(var(--sidebar-ring))',
          },
          // Circular Marketplace specific palette using HSL
          'cm-teal': {
            DEFAULT: 'hsl(var(--cm-teal))',
            50: 'hsl(var(--cm-teal-50))',
            100: 'hsl(var(--cm-teal-100))',
            200: 'hsl(var(--cm-teal-200))',
            300: 'hsl(var(--cm-teal-300))',
            400: 'hsl(var(--cm-teal-400))',
            500: 'hsl(var(--cm-teal-500))',
            600: 'hsl(var(--cm-teal-600))',
            700: 'hsl(var(--cm-teal-700))',
            800: 'hsl(var(--cm-teal-800))',
            900: 'hsl(var(--cm-teal-900))',
            950: 'hsl(var(--cm-teal-950))',
          },
          'cm-salmon': {
            DEFAULT: 'hsl(var(--cm-salmon))',
            50: 'hsl(var(--cm-salmon-50))',
            100: 'hsl(var(--cm-salmon-100))',
            200: 'hsl(var(--cm-salmon-200))',
            300: 'hsl(var(--cm-salmon-300))',
            400: 'hsl(var(--cm-salmon-400))',
            500: 'hsl(var(--cm-salmon-500))',
            600: 'hsl(var(--cm-salmon-600))',
            700: 'hsl(var(--cm-salmon-700))',
            800: 'hsl(var(--cm-salmon-800))',
            900: 'hsl(var(--cm-salmon-900))',
            950: 'hsl(var(--cm-salmon-950))',
          },
          'cm-lime': {
            DEFAULT: 'hsl(var(--cm-lime))',
            50: 'hsl(var(--cm-lime-50))',
            100: 'hsl(var(--cm-lime-100))',
            200: 'hsl(var(--cm-lime-200))',
            300: 'hsl(var(--cm-lime-300))',
            400: 'hsl(var(--cm-lime-400))',
            500: 'hsl(var(--cm-lime-500))',
            600: 'hsl(var(--cm-lime-600))',
            700: 'hsl(var(--cm-lime-700))',
            800: 'hsl(var(--cm-lime-800))',
            900: 'hsl(var(--cm-lime-900))',
            950: 'hsl(var(--cm-lime-950))',
          },
          'cm-dark': {
            DEFAULT: 'hsl(var(--cm-dark))',
            50: 'hsl(var(--cm-dark-50))',
            100: 'hsl(var(--cm-dark-100))',
            200: 'hsl(var(--cm-dark-200))',
            300: 'hsl(var(--cm-dark-300))',
            400: 'hsl(var(--cm-dark-400))',
            500: 'hsl(var(--cm-dark-500))',
            600: 'hsl(var(--cm-dark-600))',
            700: 'hsl(var(--cm-dark-700))',
            800: 'hsl(var(--cm-dark-800))',
            900: 'hsl(var(--cm-dark-900))',
            950: 'hsl(var(--cm-dark-950))',
          },
          'cm-light': {
            DEFAULT: 'hsl(var(--cm-light))',
            50: 'hsl(var(--cm-light-50))',
            100: 'hsl(var(--cm-light-100))',
            200: 'hsl(var(--cm-light-200))',
            300: 'hsl(var(--cm-light-300))',
            400: 'hsl(var(--cm-light-400))',
            500: 'hsl(var(--cm-light-500))',
            600: 'hsl(var(--cm-light-600))',
            700: 'hsl(var(--cm-light-700))',
            800: 'hsl(var(--cm-light-800))',
            900: 'hsl(var(--cm-light-900))',
            950: 'hsl(var(--cm-light-950))',
          },
        },
        borderRadius: {
          lg: 'var(--radius)',
          md: 'calc(var(--radius) - 2px)',
          sm: 'calc(var(--radius) - 4px)',
        },
        keyframes: {
          'accordion-down': {
            from: {
              height: '0',
            },
            to: {
              height: 'var(--radix-accordion-content-height)',
            },
          },
          'accordion-up': {
            from: {
              height: 'var(--radix-accordion-content-height)',
            },
            to: {
              height: '0',
            },
          },
        },
        animation: {
          'accordion-down': 'accordion-down 0.2s ease-out',
          'accordion-up': 'accordion-up 0.2s ease-out',
        },
      },
    },
    plugins: [TailwindAnimate],
  };
}
