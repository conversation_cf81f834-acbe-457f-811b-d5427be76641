/**
 * AWS Bedrock Service for AI-powered text generation
 * This service provides both mock and real AWS Bedrock functionality
 */

import {
  createInitialDescriptionPrompt,
  createItemNamePrompt,
  createRefinementPrompt,
} from './prompts';

// ===== TYPE DEFINITIONS =====

export interface BedrockConfig {
  useMockData?: boolean;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  sessionToken?: string;
  modelId?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface BedrockResponse {
  content: string;
  usage?: {
    inputTokens: number;
    outputTokens: number;
  };
}

export interface ItemGenerationContext {
  labels: string[];
  textDetections: string[];
  brand?: string;
  category?: string;
  condition?: string;
}

// ===== CONFIGURATION =====

const DEFAULT_BEDROCK_CONFIG: Required<BedrockConfig> = {
  useMockData: true,
  region: 'us-east-1',
  accessKeyId: '',
  secretAccessKey: '',
  sessionToken: '',
  modelId: 'anthropic.claude-3-haiku-20240307-v1:0',
  maxTokens: 1000,
  temperature: 0.7,
};

// ===== CORE FUNCTIONS =====

/**
 * Generates a concise, marketable product name
 */
export async function generateItemName(
  context: ItemGenerationContext,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  // Check for edge cases
  if (context.labels.length === 0 && context.textDetections.length === 0) {
    return 'Unidentified Item';
  }

  if (finalConfig.useMockData) {
    return generateMockItemName(context);
  }

  const prompt = createItemNamePrompt(context);
  const response = await callBedrockAPI(prompt, finalConfig);
  return response.content.trim();
}

/**
 * Generates an initial product description
 */
export async function generateInitialDescription(
  context: ItemGenerationContext,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  if (context.labels.length === 0) {
    return 'Item details could not be determined from the image. Please provide a detailed description manually.';
  }

  if (finalConfig.useMockData) {
    return generateMockInitialDescription(context);
  }

  const prompt = createInitialDescriptionPrompt(context);
  const response = await callBedrockAPI(prompt, finalConfig);
  return response.content.trim();
}

/**
 * Refines and enhances a description for marketplace appeal
 */
export async function refineDescription(
  initialDescription: string,
  context: ItemGenerationContext,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  if (finalConfig.useMockData) {
    return refineMockDescription(initialDescription, context);
  }

  const prompt = createRefinementPrompt(initialDescription, context);
  const response = await callBedrockAPI(prompt, finalConfig);
  return response.content.trim();
}

// ===== AWS BEDROCK API =====

/**
 * Calls AWS Bedrock API with the given prompt
 */
async function callBedrockAPI(
  prompt: string,
  config: Required<BedrockConfig>
): Promise<BedrockResponse> {
  // Import AWS SDK dynamically to avoid issues in browser environments
  const { BedrockRuntimeClient, InvokeModelCommand } = await import(
    '@aws-sdk/client-bedrock-runtime'
  );

  const client = new BedrockRuntimeClient({
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
      ...(config.sessionToken && { sessionToken: config.sessionToken }),
    },
  });

  const requestBody = {
    anthropic_version: 'bedrock-2023-05-31',
    max_tokens: config.maxTokens,
    temperature: config.temperature,
    messages: [
      {
        role: 'user',
        content: prompt,
      },
    ],
  };

  try {
    const command = new InvokeModelCommand({
      modelId: config.modelId,
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(requestBody),
    });

    const response = await client.send(command);
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    return {
      content: responseBody.content[0].text,
      usage: {
        inputTokens: responseBody.usage?.input_tokens || 0,
        outputTokens: responseBody.usage?.output_tokens || 0,
      },
    };
  } catch (error) {
    console.error('AWS Bedrock API error:', error);
    throw new Error(`AWS Bedrock API call failed: ${error}`);
  }
}

// ===== MOCK IMPLEMENTATIONS =====

/**
 * Generates a mock item name for development/testing
 */
function generateMockItemName(context: ItemGenerationContext): string {
  // Use detected text first if available
  if (context.textDetections.length > 0) {
    const text = context.textDetections[0];
    if (
      context.brand &&
      !text.toLowerCase().includes(context.brand.toLowerCase())
    ) {
      return `${context.brand} ${text}`;
    }
    return text;
  }

  // Use labels to create a name
  if (context.labels.length > 0) {
    const mainItem = context.labels[0];
    if (context.brand) {
      return `${context.brand} ${mainItem}`;
    }
    return mainItem;
  }

  return 'Unidentified Item';
}

/**
 * Generates a mock initial description for development/testing
 */
function generateMockInitialDescription(
  context: ItemGenerationContext
): string {
  if (context.labels.length === 0) {
    return 'Item details could not be determined from the image. Please provide a detailed description manually.';
  }

  const mainItem = context.labels[0];
  let description = `This ${mainItem.toLowerCase()}`;

  if (context.brand) {
    description += ` by ${context.brand}`;
  }

  if (context.textDetections.length > 0) {
    const modelInfo = context.textDetections.join(', ');
    description += ` (${modelInfo})`;
  }

  const additionalLabels = context.labels.slice(1, 3);
  if (additionalLabels.length > 0) {
    description += ` features ${additionalLabels
      .map((label) => label.toLowerCase())
      .join(' and ')} characteristics`;
  }

  description += '. Please review the photos and description for full details.';

  return description;
}

/**
 * Refines a description using enhanced mock logic
 */
function refineMockDescription(
  initialDescription: string,
  context: ItemGenerationContext
): string {
  let refined = initialDescription;

  // Add category-specific enhancements
  const categoryEnhancements = getCategoryEnhancements(context.category);
  if (categoryEnhancements) {
    refined += ` ${categoryEnhancements}`;
  }

  // Add condition-specific details
  const conditionDetails = getConditionDetails(context.condition);
  if (conditionDetails) {
    refined += ` ${conditionDetails}`;
  }

  // Add marketplace-specific call to action
  refined +=
    ' Please review all photos and contact the seller for additional details.';

  return refined;
}

// ===== HELPER FUNCTIONS =====

function getCategoryEnhancements(category?: string): string | null {
  const enhancements: Record<string, string> = {
    Electronics:
      'Perfect for tech enthusiasts seeking reliable, high-quality electronics.',
    Furniture:
      'An excellent addition to any home or office, combining style and functionality.',
    Clothing: 'Stylish and comfortable, ideal for fashion-conscious buyers.',
    Books: 'A valuable addition to any library or collection.',
    Sports: 'Essential gear for fitness enthusiasts and active lifestyles.',
    Toys: 'Great for children and collectors who appreciate quality items.',
    Home: 'Perfect for organizing and enhancing your living space.',
    Automotive: 'Quality automotive equipment for vehicle enthusiasts.',
  };

  return category ? enhancements[category] || null : null;
}

function getConditionDetails(condition?: string): string | null {
  // Only return condition details if explicitly provided by seller
  // Avoid making assumptions about condition from image analysis
  const details: Record<string, string> = {
    'Like New': 'Seller indicates this item is in like-new condition.',
    Good: 'Seller indicates this item is in good condition.',
    Fair: 'Seller indicates this item is in fair condition.',
    Poor: 'Seller indicates this item shows wear and is priced accordingly.',
  };

  return condition ? details[condition] || null : null;
}

// ===== EXPORTS =====

export default {
  generateItemName,
  generateInitialDescription,
  refineDescription,
};
