/**
 * Enhanced prompt generation for AWS Bedrock
 * Improved prompts for better marketplace descriptions
 */

import type { ItemGenerationContext } from './bedrock';

// ===== PROMPT TEMPLATES =====

/**
 * Creates an enhanced prompt for generating item names
 */
function createItemNamePrompt(context: ItemGenerationContext): string {
  let prompt = `You are an expert marketplace copywriter specializing in creating compelling product titles that drive sales.

TASK: Create a concise, searchable product title (4-8 words maximum) that buyers will find appealing and easy to discover.

IMAGE ANALYSIS DATA:
- Detected Objects: ${context.labels.join(', ')}`;

  if (context.textDetections.length > 0) {
    prompt += `\n- Visible Text/Model Info: ${context.textDetections.join(
      ', '
    )}`;
  }

  if (context.brand) {
    prompt += `\n- Brand: ${context.brand}`;
  }

  if (context.category) {
    prompt += `\n- Category: ${context.category}`;
  }

  prompt += `

TITLE REQUIREMENTS:
- 4-8 words maximum for optimal searchability
- Include brand name if detected and relevant
- Use specific model/version info when available
- Prioritize the most distinctive features
- Make it appealing to potential buyers
- Avoid generic terms like "item" or "object"

EXAMPLES OF GOOD TITLES:
- "Apple MacBook Pro 13-inch M1"
- "Nike Air Jordan Retro Sneakers"
- "Vintage Oak Dining Table"
- "Canon EOS R5 Camera Body"

Generate only the product title, no quotes or additional text:`;

  return prompt;
}

/**
 * Creates an enhanced prompt for generating initial descriptions
 */
function createInitialDescriptionPrompt(
  context: ItemGenerationContext
): string {
  let prompt = `You are an expert marketplace copywriter creating product descriptions that convert browsers into buyers.

TASK: Write a compelling 3-4 sentence product description that highlights key features and benefits.

IMAGE ANALYSIS DATA:
- Detected Objects: ${context.labels.join(', ')}`;

  if (context.textDetections.length > 0) {
    prompt += `\n- Visible Text/Model Info: ${context.textDetections.join(
      ', '
    )}`;
  }

  if (context.brand) {
    prompt += `\n- Brand: ${context.brand}`;
  }

  if (context.category) {
    prompt += `\n- Category: ${context.category}`;
  }

  if (context.condition) {
    prompt += `\n- Condition: ${context.condition}`;
  }

  prompt += `

DESCRIPTION REQUIREMENTS:
- 3-4 clear, engaging sentences
- Lead with the most important feature/benefit
- Include specific details from the image analysis
- Mention brand and model prominently if available
- Focus on what makes this item valuable to buyers
- Use active, positive language
- Be factual and avoid assumptions about condition
- Let buyers assess condition from photos

WRITING STYLE:
- Professional yet approachable
- Benefit-focused (what's in it for the buyer)
- Specific rather than generic
- Trustworthy and conservative
- Avoid making claims about condition unless explicitly provided

Generate only the description, no additional text:`;

  return prompt;
}

/**
 * Creates an enhanced prompt for refining descriptions
 */
function createRefinementPrompt(
  initialDescription: string,
  context: ItemGenerationContext
): string {
  let prompt = `You are a senior marketplace copywriter tasked with optimizing a product description for maximum appeal and conversion.

TASK: Transform the initial description into a compelling, marketplace-optimized version that drives sales.

INITIAL DESCRIPTION:
"${initialDescription}"

PRODUCT CONTEXT:`;

  if (context.category) {
    prompt += `\n- Category: ${context.category}`;
  }

  if (context.condition) {
    prompt += `\n- Condition: ${context.condition}`;
  }

  if (context.brand) {
    prompt += `\n- Brand: ${context.brand}`;
  }

  prompt += `\n- Key Features: ${context.labels.slice(0, 3).join(', ')}`;

  prompt += `

OPTIMIZATION REQUIREMENTS:
- Enhance appeal while maintaining accuracy
- Add compelling selling points specific to this category
- Only include condition details if explicitly provided
- Avoid making assumptions about item condition
- Create interest without being pushy or overstating
- Ensure the description flows naturally
- Keep it concise but comprehensive (4-5 sentences max)
- End with a subtle call-to-action encouraging photo review

CATEGORY-SPECIFIC ENHANCEMENTS:`;

  // Add category-specific guidance
  const categoryGuidance = getCategoryGuidance(context.category);
  if (categoryGuidance) {
    prompt += `\n${categoryGuidance}`;
  }

  prompt += `

CONDITION-SPECIFIC LANGUAGE:`;

  // Add condition-specific guidance
  const conditionGuidance = getConditionGuidance(context.condition);
  if (conditionGuidance) {
    prompt += `\n${conditionGuidance}`;
  }

  prompt += `

Generate only the refined description, no additional text:`;

  return prompt;
}

// ===== CATEGORY-SPECIFIC GUIDANCE =====

function getCategoryGuidance(category?: string): string | null {
  const guidance: Record<string, string> = {
    Electronics: `
- Emphasize performance, reliability, and technical specifications
- Mention compatibility and included accessories
- Highlight energy efficiency or advanced features
- Appeal to tech enthusiasts and practical users`,

    Furniture: `
- Focus on style, comfort, and space enhancement
- Mention materials, craftsmanship, and durability
- Describe how it fits into different decor styles
- Emphasize functionality and storage if applicable`,

    Clothing: `
- Highlight style, comfort, and versatility
- Mention fabric quality and care instructions
- Describe fit and sizing accurately
- Appeal to fashion-conscious buyers`,

    Books: `
- Emphasize knowledge value and entertainment
- Mention edition, condition of pages, and rarity if applicable
- Appeal to collectors and readers
- Highlight educational or entertainment value`,

    Sports: `
- Focus on performance enhancement and durability
- Mention professional or recreational use
- Highlight safety features and quality construction
- Appeal to fitness enthusiasts and athletes`,

    Toys: `
- Emphasize fun, educational value, and safety
- Mention age appropriateness and developmental benefits
- Highlight collectible value if applicable
- Appeal to parents and collectors`,

    Home: `
- Focus on organization, convenience, and home improvement
- Mention space-saving or efficiency benefits
- Highlight quality and durability
- Appeal to homeowners and renters`,

    Automotive: `
- Emphasize performance, safety, and compatibility
- Mention installation requirements and included parts
- Highlight durability and manufacturer specifications
- Appeal to car enthusiasts and practical drivers`,
  };

  return category ? guidance[category] || null : null;
}

// ===== CONDITION-SPECIFIC GUIDANCE =====

function getConditionGuidance(condition?: string): string | null {
  const guidance: Record<string, string> = {
    'Like New': `
- Only mention condition if explicitly provided by seller
- Focus on features and functionality rather than condition claims
- Encourage buyers to review photos carefully
- Emphasize value and features`,

    Good: `
- Only reference condition if explicitly provided by seller
- Focus on functionality and features
- Encourage photo review for condition assessment
- Emphasize practical benefits`,

    Fair: `
- Only mention condition if explicitly provided by seller
- Focus on functionality and value proposition
- Encourage thorough photo review
- Emphasize practical benefits and affordability`,

    Poor: `
- Only reference condition if explicitly provided by seller
- Focus on functionality, parts value, or restoration potential
- Strongly encourage photo review and inspection
- Be honest about any known limitations`,
  };

  return condition ? guidance[condition] || null : null;
}

// ===== EXPORTS =====

export {
  createInitialDescriptionPrompt,
  createItemNamePrompt,
  createRefinementPrompt,
};
