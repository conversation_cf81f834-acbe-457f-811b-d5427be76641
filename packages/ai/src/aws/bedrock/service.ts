import {
  BedrockRuntime,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';

import { backendConfig } from '@package/configs';

export function getBedrockService() {
  const config = backendConfig.aws;

  // Check if we should use mock mode (for development/testing)
  const useMockMode =
    process.env.USE_MOCK_BEDROCK === 'true' ||
    !config.credentials.accessKeyId ||
    !config.credentials.secretAccessKey;

  if (useMockMode) {
    console.log('[Bedrock] Using mock mode - no real AWS calls will be made');
    return {
      send: async (prompt: string) => {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Return mock response based on prompt content
        let mockContent =
          'Generated content based on the provided information.';

        if (
          prompt.toLowerCase().includes('item name') ||
          prompt.toLowerCase().includes('title')
        ) {
          mockContent = 'High-Quality Item';
        } else if (prompt.toLowerCase().includes('description')) {
          mockContent =
            'This is a high-quality item with excellent features. Perfect for buyers looking for reliable products. Please review all photos and contact the seller for additional details.';
        } else if (prompt.toLowerCase().includes('refin')) {
          mockContent =
            'This premium item offers exceptional value and functionality. Ideal for discerning buyers who appreciate quality craftsmanship. All photos accurately represent the item condition - please review carefully before purchase.';
        }

        return {
          content: mockContent,
          usage: {
            inputTokens: prompt.length / 4, // Rough estimate
            outputTokens: mockContent.length / 4,
          },
        };
      },
    };
  }

  const bedrockClient = new BedrockRuntime({
    region: config.region,
    credentials: config.credentials,
  });

  async function invokeModel(command: InvokeModelCommand) {
    return bedrockClient.send(command);
  }

  return {
    send: async (prompt: string) => {
      try {
        const command = new InvokeModelCommand({
          modelId: config.bedrock.modelId,
          contentType: 'application/json',
          accept: 'application/json',
          body: JSON.stringify({
            anthropic_version: 'bedrock-2023-05-31',
            max_tokens: config.bedrock.maxTokens,
            temperature: config.bedrock.temperature,
            messages: [
              {
                role: 'user',
                content: prompt,
              },
            ],
          }),
        });

        const response = await invokeModel(command);
        const responseBody = JSON.parse(
          new TextDecoder().decode(response.body)
        );

        return {
          content: responseBody.content[0].text,
          usage: {
            inputTokens: responseBody.usage?.input_tokens || 0,
            outputTokens: responseBody.usage?.output_tokens || 0,
          },
        };
      } catch (error) {
        console.error(
          '[Bedrock] API call failed, falling back to mock response:',
          error
        );

        // Fallback to mock response when AWS call fails
        let fallbackContent =
          'Generated content (fallback mode due to API error).';

        if (
          prompt.toLowerCase().includes('item name') ||
          prompt.toLowerCase().includes('title')
        ) {
          fallbackContent = 'Quality Item';
        } else if (prompt.toLowerCase().includes('description')) {
          fallbackContent =
            'This item offers good value and functionality. Please review all photos and contact the seller for additional details about condition and features.';
        } else if (prompt.toLowerCase().includes('refin')) {
          fallbackContent =
            'This item provides reliable performance and good value. Suitable for buyers seeking quality products. Please review all photos carefully and contact the seller with any questions.';
        }

        return {
          content: fallbackContent,
          usage: {
            inputTokens: prompt.length / 4,
            outputTokens: fallbackContent.length / 4,
          },
        };
      }
    },
  };
}
