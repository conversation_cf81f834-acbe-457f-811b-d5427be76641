import {
  BedrockRuntime,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';

import { backendConfig } from '@package/configs';

export function getBedrockService() {
  const config = backendConfig.aws;
  const bedrockClient = new BedrockRuntime({
    region: config.region,
    credentials: config.credentials,
  });

  async function invokeModel(command: InvokeModelCommand) {
    return bedrockClient.send(command);
  }

  return {
    send: async (prompt: string) => {
      const command = new InvokeModelCommand({
        modelId: config.bedrock.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          anthropic_version: 'bedrock-2023-05-31',
          max_tokens: config.bedrock.maxTokens,
          temperature: config.bedrock.temperature,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
        }),
      });

      const response = await invokeModel(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      return {
        content: responseBody.content[0].text,
        usage: {
          inputTokens: responseBody.usage?.input_tokens || 0,
          outputTokens: responseBody.usage?.output_tokens || 0,
        },
      };
    },
  };
}
