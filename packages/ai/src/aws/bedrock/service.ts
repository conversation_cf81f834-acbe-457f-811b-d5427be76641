import {
  BedrockRuntime,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';

import { backendConfig } from '@package/configs';

export function getBedrockService() {
  const config = backendConfig.aws;
  const bedrockClient = new BedrockRuntime({
    region: config.region,
    credentials: config.credentials,
  });

  async function invokeModel(command: InvokeModelCommand) {
    return bedrockClient.send(command);
  }

  return {
    send: async (prompt: string) => {
      const command = new InvokeModelCommand({
        modelId: 'anthropic.claude-3-haiku-20240307-v1:0',
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify({
          prompt: prompt,
          temperature: 0.7,
          max_tokens: 1000,
          anthropic_version: 'bedrock-2023-05-31',
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
        }),
      });

      const response = await invokeModel(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));

      return {
        content: responseBody.content[0].text,
        usage: {
          inputTokens: responseBody.usage?.input_tokens || 0,
          outputTokens: responseBody.usage?.output_tokens || 0,
        },
      };
    },
  };
}
