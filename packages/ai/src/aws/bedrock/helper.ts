function getCategoryEnhancements(category?: string): string | null {
  const enhancements: Record<string, string> = {
    Electronics:
      'Perfect for tech enthusiasts seeking reliable, high-quality electronics.',
    Furniture:
      'An excellent addition to any home or office, combining style and functionality.',
    Clothing: 'Stylish and comfortable, ideal for fashion-conscious buyers.',
    Books: 'A valuable addition to any library or collection.',
    Sports: 'Essential gear for fitness enthusiasts and active lifestyles.',
    Toys: 'Great for children and collectors who appreciate quality items.',
    Home: 'Perfect for organizing and enhancing your living space.',
    Automotive: 'Quality automotive equipment for vehicle enthusiasts.',
  };

  return category ? enhancements[category] || null : null;
}

function getConditionDetails(condition?: string): string | null {
  // Only return condition details if explicitly provided by seller
  // Avoid making assumptions about condition from image analysis
  const details: Record<string, string> = {
    'Like New': 'Seller indicates this item is in like-new condition.',
    Good: 'Seller indicates this item is in good condition.',
    Fair: 'Seller indicates this item is in fair condition.',
    Poor: 'Seller indicates this item shows wear and is priced accordingly.',
  };

  return condition ? details[condition] || null : null;
}
