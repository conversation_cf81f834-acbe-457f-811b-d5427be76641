/**
 * Helper functions for AWS Rekognition
 */
import {
  RekognitionFaceDetail,
  RekognitionLabel,
  RekognitionModerationLabel,
  RekognitionTextDetection,
} from './types';

/**
 * Checks for content moderation issues
 */
export function checkContentModeration(
  moderationLabels: RekognitionModerationLabel[]
): string | undefined {
  if (moderationLabels.length === 0) return undefined;

  const highConfidenceLabels = moderationLabels.filter(
    (label) => label.Confidence > 75
  );

  if (highConfidenceLabels.length === 0) return undefined;

  // Categorize moderation issues
  const nudityLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('nudity')
  );
  const violenceLabels = highConfidenceLabels.filter(
    (label) =>
      label.Name.toLowerCase().includes('violence') ||
      label.Name.toLowerCase().includes('weapon')
  );
  const drugLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('drug')
  );
  const suggestiveLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('suggestive')
  );

  if (nudityLabels.length > 0) {
    return 'This image contains nudity or explicit content and cannot be used for marketplace listings. Please upload a different image that complies with our community guidelines.';
  }

  if (violenceLabels.length > 0) {
    return 'This image contains violent content, weapons, or war-related material and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (drugLabels.length > 0) {
    return 'This image contains drug-related content and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (suggestiveLabels.length > 0) {
    return 'This image contains inappropriate or suggestive content and cannot be used for marketplace listings. Please upload a different image.';
  }

  // Generic moderation warning
  return `This image contains content that violates our community guidelines (${highConfidenceLabels[0].Name}) and cannot be used for marketplace listings.`;
}

/**
 * Checks for face detection and privacy concerns
 */
export function checkFaceDetection(
  faceDetails: RekognitionFaceDetail[]
): string | undefined {
  if (faceDetails.length === 0) return undefined;

  const highConfidenceFaces = faceDetails.filter(
    (face) => face.Confidence > 90
  );

  if (highConfidenceFaces.length === 0) return undefined;

  if (highConfidenceFaces.length === 1) {
    return 'We detected a face in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.';
  }

  return `We detected ${highConfidenceFaces.length} faces in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.`;
}

/**
 * Determines item category from detected labels
 */
export function determineCategory(labels: string[]): string {
  const categoryMappings: Record<string, string[]> = {
    Electronics: [
      'Laptop',
      'Computer',
      'Mobile Phone',
      'Smartphone',
      'Electronics',
      'Device',
      'Technology',
    ],
    Furniture: ['Furniture', 'Chair', 'Table', 'Desk', 'Seat', 'Wood'],
    Clothing: ['Clothing', 'Apparel', 'Shirt', 'Fashion', 'Garment'],
    Books: ['Book', 'Publication', 'Text', 'Literature', 'Novel'],
    Sports: ['Sports', 'Equipment', 'Ball', 'Fitness', 'Exercise'],
    Toys: ['Toy', 'Game', 'Doll', 'Puzzle', 'Entertainment'],
    Home: ['Home', 'Kitchen', 'Appliance', 'Tool', 'Utensil'],
    Automotive: ['Car', 'Vehicle', 'Automotive', 'Transportation'],
  };

  for (const [category, keywords] of Object.entries(categoryMappings)) {
    if (labels.some((label) => keywords.includes(label))) {
      return category;
    }
  }

  return 'Other';
}

/**
 * Determines item condition from labels
 */
export function determineCondition(labels: string[]): string {
  // Look for condition-related keywords in labels
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect'],
    Good: ['good', 'clean', 'working', 'functional'],
    Fair: ['used', 'worn', 'old', 'vintage'],
    Poor: ['damaged', 'broken', 'cracked', 'torn'],
  };

  for (const [condition, keywords] of Object.entries(conditionKeywords)) {
    if (
      labels.some((label) =>
        keywords.some((keyword) =>
          label.toLowerCase().includes(keyword.toLowerCase())
        )
      )
    ) {
      return condition;
    }
  }

  // Default condition based on item type
  if (labels.includes('Electronics') || labels.includes('Technology')) {
    return 'Good';
  }

  return 'Good'; // Default fallback
}

/**
 * Extracts brand information from AWS labels
 */
export function extractBrandFromLabels(labels: any[]): string | undefined {
  // Look for common brand indicators in labels
  const brandKeywords = [
    'Apple',
    'Samsung',
    'Nike',
    'Adidas',
    'Sony',
    'Microsoft',
    'Google',
    'Amazon',
    'Dell',
    'HP',
    'Lenovo',
    'Canon',
    'Nikon',
  ];

  for (const label of labels) {
    const labelName = label.Name || '';
    for (const brand of brandKeywords) {
      if (labelName.toLowerCase().includes(brand.toLowerCase())) {
        return brand;
      }
    }
  }

  return undefined;
}

/**
 * Extracts condition hints from AWS labels
 */
export function extractConditionFromLabels(labels: any[]): string | undefined {
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect', 'unused'],
    Good: ['good', 'clean', 'working', 'functional', 'excellent'],
    Fair: ['used', 'worn', 'old', 'vintage', 'scratched'],
    Poor: ['damaged', 'broken', 'cracked', 'torn', 'defective'],
  };

  for (const label of labels) {
    const labelName = (label.Name || '').toLowerCase();
    for (const [condition, keywords] of Object.entries(conditionKeywords)) {
      if (keywords.some((keyword) => labelName.includes(keyword))) {
        return condition;
      }
    }
  }

  return undefined;
}

/**
 * Determines confidence level for AI generation
 */
export function determineGenerationConfidence(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string
): 'high' | 'medium' | 'low' {
  const highConfidenceLabels = labels.filter((label) => label.Confidence > 90);
  const highConfidenceText = textDetections.filter(
    (text) => text.Confidence > 90
  );

  if (
    highConfidenceLabels.length >= 3 &&
    (highConfidenceText.length > 0 || brand)
  ) {
    return 'high';
  }

  if (highConfidenceLabels.length >= 2 || highConfidenceText.length > 0) {
    return 'medium';
  }

  return 'low';
}
