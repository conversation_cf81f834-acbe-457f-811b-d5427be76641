/**
 * Mock data for Rekognition API
 */
import { RekognitionResponse } from './types';

// Mock response data for testing purposes
export const MOCK_RESPONSES: Record<string, RekognitionResponse> = {
  laptop: {
    Labels: [
      { Name: 'Laptop', Confidence: 98.7 },
      { Name: 'Computer', Confidence: 98.7 },
      { Name: 'Electronics', Confidence: 98.7 },
      { Name: 'PC', Confidence: 95.2 },
      { Name: 'MacBook', Confidence: 89.5 },
      { Name: 'Device', Confidence: 98.7 },
      { Name: 'Technology', Confidence: 98.7 },
    ],
    ModerationLabels: [],
    TextDetections: [
      {
        DetectedText: 'MacBook Pro',
        Type: 'LINE',
        Confidence: 92.4,
      },
      {
        DetectedText: 'Intel Core i7',
        Type: 'LINE',
        Confidence: 90.1,
      },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Good',
  },
  phone: {
    Labels: [
      { Name: 'Mobile Phone', Confidence: 98.3 },
      { Name: 'Phone', Confidence: 98.3 },
      { Name: 'Smartphone', Confidence: 98.3 },
      { Name: 'Electronics', Confidence: 98.3 },
      { Name: 'iPhone', Confidence: 87.2 },
      { Name: 'Device', Confidence: 98.3 },
    ],
    ModerationLabels: [],
    TextDetections: [
      {
        DetectedText: 'iPhone 14',
        Type: 'LINE',
        Confidence: 93.5,
      },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Like New',
  },
  furniture: {
    Labels: [
      { Name: 'Furniture', Confidence: 97.5 },
      { Name: 'Chair', Confidence: 97.5 },
      { Name: 'Wood', Confidence: 94.2 },
      { Name: 'Seat', Confidence: 93.8 },
      { Name: 'Home Decor', Confidence: 91.5 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Good',
  },
  clothing: {
    Labels: [
      { Name: 'Clothing', Confidence: 98.1 },
      { Name: 'Apparel', Confidence: 98.1 },
      { Name: 'Shirt', Confidence: 96.8 },
      { Name: 'T-Shirt', Confidence: 92.3 },
      { Name: 'Fashion', Confidence: 90.7 },
    ],
    ModerationLabels: [],
    TextDetections: [
      {
        DetectedText: 'Nike',
        Type: 'LINE',
        Confidence: 89.6,
      },
    ],
    FaceDetails: [],
    Brand: 'Nike',
    ConditionHint: 'Good',
  },
  book: {
    Labels: [
      { Name: 'Book', Confidence: 98.9 },
      { Name: 'Publication', Confidence: 98.9 },
      { Name: 'Text', Confidence: 94.7 },
      { Name: 'Literature', Confidence: 92.6 },
      { Name: 'Novel', Confidence: 91.2 },
    ],
    ModerationLabels: [],
    TextDetections: [
      {
        DetectedText: 'The Great Gatsby',
        Type: 'LINE',
        Confidence: 95.3,
      },
      {
        DetectedText: 'F. Scott Fitzgerald',
        Type: 'LINE',
        Confidence: 94.1,
      },
    ],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  inappropriate: {
    Labels: [
      { Name: 'Person', Confidence: 99.1 },
      { Name: 'Human', Confidence: 99.1 },
    ],
    ModerationLabels: [
      { Name: 'Explicit Nudity', Confidence: 86.5 },
      { Name: 'Nudity', Confidence: 92.7 },
    ],
    TextDetections: [],
    FaceDetails: [{ Confidence: 99.8 }],
  },
  face: {
    Labels: [
      { Name: 'Person', Confidence: 99.3 },
      { Name: 'Human', Confidence: 99.3 },
      { Name: 'Face', Confidence: 99.3 },
      { Name: 'Portrait', Confidence: 95.6 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [{ Confidence: 99.9 }],
  },
};
