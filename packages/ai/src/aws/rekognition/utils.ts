/**
 * Utility functions for AWS Rekognition
 */

/**
 * Converts File to Uint8Array for AWS SDK
 * Works in both browser and Node.js environments
 */
export async function fileToBytes(file: File): Promise<Uint8Array> {
  // Check if we're in a browser environment
  if (typeof FileReader !== 'undefined') {
    // Browser environment - use FileReader
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(new Uint8Array(reader.result));
        } else {
          reject(new Error('Failed to read file as ArrayBuffer'));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
  } else {
    // Node.js environment - use arrayBuffer method
    try {
      const arrayBuffer = await file.arrayBuffer();
      return new Uint8Array(arrayBuffer);
    } catch (error) {
      throw new Error(`Failed to convert file to bytes: ${error}`);
    }
  }
}

/**
 * Generates AI-powered description using mock Bedrock functionality
 * This is a fallback function for when Bedrock is not available
 */
export async function generateBasicDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  // Simulate Bedrock API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 500 + Math.random() * 500)
  );

  // Build description based on detected elements
  let description = '';

  // Start with the main item
  if (labels.length > 0) {
    const mainItem = labels[0];
    description = `This ${mainItem.toLowerCase()}`;

    // Add brand if detected
    if (brand) {
      description += ` by ${brand}`;
    }

    // Add model/text information if available
    if (textDetections.length > 0) {
      const modelInfo = textDetections.join(', ');
      description += ` (${modelInfo})`;
    }

    // Add descriptive details based on other labels
    const additionalLabels = labels.slice(1, 3);
    if (additionalLabels.length > 0) {
      description += ` features ${additionalLabels
        .map((label) => label.toLowerCase())
        .join(' and ')} characteristics`;
    }

    // Add condition-based description
    description +=
      '. This item appears to be in good condition based on the image analysis';

    // Add call to action
    description +=
      '. Please review the details and condition before making your purchase decision.';
  } else {
    description =
      'Item details could not be automatically determined from the image. Please provide a detailed description manually.';
  }

  return description;
}
