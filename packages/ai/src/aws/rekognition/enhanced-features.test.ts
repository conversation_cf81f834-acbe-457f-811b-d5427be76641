/**
 * Tests for enhanced AI features
 */

import {
  generateAIContent,
  generateInitialDescription,
  generateItemName,
  refineDescriptionWithBedrock,
  type RekognitionLabel,
  type RekognitionResponse,
  type RekognitionTextDetection,
} from './rekognition';

// Mock data for testing
const mockLabels: RekognitionLabel[] = [
  { Name: 'Laptop', Confidence: 95.5 },
  { Name: 'Computer', Confidence: 92.3 },
  { Name: 'Electronics', Confidence: 89.7 },
];

const mockTextDetections: RekognitionTextDetection[] = [
  { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
  { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
];

const mockRekognitionResponse: RekognitionResponse = {
  Labels: mockLabels,
  ModerationLabels: [],
  TextDetections: mockTextDetections,
  FaceDetails: [],
  Brand: 'Apple',
  ConditionHint: 'Good',
};

describe('Enhanced AI Features', () => {
  // Use mock data for all tests
  const mockConfig = { useMockData: true };

  describe('generateItemName', () => {
    it('should generate item name with brand and text', async () => {
      const itemName = await generateItemName(
        mockLabels,
        mockTextDetections,
        'Apple',
        mockConfig
      );

      expect(itemName).toBeTruthy();
      expect(typeof itemName).toBe('string');
      expect(itemName.length).toBeGreaterThan(0);
      expect(itemName.toLowerCase()).toContain('apple');
    });

    it('should handle missing text detections', async () => {
      const itemName = await generateItemName(
        mockLabels,
        [],
        'Apple',
        mockConfig
      );

      expect(itemName).toBeTruthy();
      expect(typeof itemName).toBe('string');
    });

    it('should handle missing brand', async () => {
      const itemName = await generateItemName(
        mockLabels,
        mockTextDetections,
        undefined,
        mockConfig
      );

      expect(itemName).toBeTruthy();
      expect(typeof itemName).toBe('string');
    });

    it('should return fallback for empty data', async () => {
      const itemName = await generateItemName([], [], undefined, mockConfig);

      expect(itemName).toBe('Unidentified Item');
    });
  });

  describe('generateInitialDescription', () => {
    it('should generate description with all data', async () => {
      const description = await generateInitialDescription(
        mockLabels,
        mockTextDetections,
        'Apple',
        mockConfig
      );

      expect(description).toBeTruthy();
      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(50);
      expect(description.toLowerCase()).toContain('laptop');
    });

    it('should handle missing data gracefully', async () => {
      const description = await generateInitialDescription(
        [],
        [],
        undefined,
        mockConfig
      );

      expect(description).toBeTruthy();
      expect(typeof description).toBe('string');
      expect(description).toContain('could not be determined');
    });
  });

  describe('refineDescriptionWithBedrock', () => {
    it('should refine description with category and condition', async () => {
      const initialDescription =
        'This laptop by Apple features computer and electronics characteristics.';

      const refinedDescription = await refineDescriptionWithBedrock(
        initialDescription,
        'Apple MacBook Pro',
        'Electronics',
        'Good',
        mockConfig
      );

      expect(refinedDescription).toBeTruthy();
      expect(typeof refinedDescription).toBe('string');
      expect(refinedDescription.length).toBeGreaterThan(
        initialDescription.length
      );
      expect(refinedDescription).toContain('tech enthusiasts');
    });
  });

  describe('generateAIContent', () => {
    it('should generate complete AI content', async () => {
      const aiContent = await generateAIContent(
        mockRekognitionResponse,
        mockConfig
      );

      expect(aiContent).toBeTruthy();
      expect(aiContent.itemName).toBeTruthy();
      expect(aiContent.initialDescription).toBeTruthy();
      expect(aiContent.refinedDescription).toBeTruthy();
      expect(aiContent.generationMethod).toBe('ai');
      expect(['high', 'medium', 'low']).toContain(aiContent.confidence);
    });

    it('should handle zero analysis response', async () => {
      const zeroResponse: RekognitionResponse = {
        Labels: [],
        ModerationLabels: [],
        TextDetections: [],
        FaceDetails: [],
      };

      const aiContent = await generateAIContent(zeroResponse, mockConfig);

      expect(aiContent.itemName).toBe('Unidentified Item');
      expect(aiContent.initialDescription).toContain('could not be determined');
      expect(aiContent.confidence).toBe('low');
    });

    it('should handle unrecognized items', async () => {
      const unrecognizedResponse: RekognitionResponse = {
        Labels: [
          { Name: 'Object', Confidence: 45.2 },
          { Name: 'Unidentified', Confidence: 32.1 },
        ],
        ModerationLabels: [],
        TextDetections: [],
        FaceDetails: [],
      };

      const aiContent = await generateAIContent(
        unrecognizedResponse,
        mockConfig
      );

      expect(aiContent.itemName).toBeTruthy();
      expect(typeof aiContent.itemName).toBe('string');
      expect(aiContent.confidence).toBe('low');
    });
  });

  describe('Error handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      // This should still work with mock data even with invalid config
      const result = await generateAIContent(mockRekognitionResponse, {
        useMockData: true,
        region: 'invalid-region',
        accessKeyId: 'invalid',
        secretAccessKey: 'invalid',
      });

      expect(result.generationMethod).toBe('ai');
    });
  });
});
