import {
  DetectFacesCommand,
  DetectLabelsCommand,
  DetectModerationLabelsCommand,
  DetectTextCommand,
  RekognitionClient,
} from '@aws-sdk/client-rekognition';

import { backendConfig } from '@package/configs';

import { extractBrandFromLabels, extractConditionFromLabels } from './helpers';
import { fileToBytes } from './utils';

export function getRekognitionService() {
  const config = backendConfig.aws;
  const rekognitionClient = new RekognitionClient({
    region: config.region,
    credentials: config.credentials,
  });

  async function detectLabels(imageBytes: Uint8Array) {
    const command = new DetectLabelsCommand({
      Image: { Bytes: imageBytes },
      MaxLabels: 20,
      MinConfidence: 80,
    });

    return rekognitionClient.send(command);
  }

  async function detectText(imageBytes: Uint8Array) {
    const command = new DetectTextCommand({
      Image: { Bytes: imageBytes },
    });

    return rekognitionClient.send(command);
  }

  async function detectModerationLabels(imageBytes: Uint8Array) {
    const command = new DetectModerationLabelsCommand({
      Image: { Bytes: imageBytes },
      MinConfidence: 75,
    });

    return rekognitionClient.send(command);
  }

  async function detectFaces(imageBytes: Uint8Array) {
    const command = new DetectFacesCommand({
      Image: { Bytes: imageBytes },
      Attributes: ['DEFAULT'],
    });

    return rekognitionClient.send(command);
  }

  return {
    analyse: async (imageFile: File) => {
      const imageBytes = await fileToBytes(imageFile);

      const [labelsResult, textResult, moderationResult, facesResult] =
        await Promise.allSettled([
          detectLabels(imageBytes),
          detectText(imageBytes),
          detectModerationLabels(imageBytes),
          detectFaces(imageBytes),
        ]);

      const labels =
        labelsResult.status === 'fulfilled'
          ? labelsResult.value.Labels || []
          : [];
      const textDetections =
        textResult.status === 'fulfilled'
          ? textResult.value.TextDetections || []
          : [];
      const moderationLabels =
        moderationResult.status === 'fulfilled'
          ? moderationResult.value.ModerationLabels || []
          : [];
      const faceDetails =
        facesResult.status === 'fulfilled'
          ? facesResult.value.FaceDetails || []
          : [];

      // Convert AWS SDK response to our format
      return {
        Labels: labels.map((label) => ({
          Name: label.Name || '',
          Confidence: label.Confidence || 0,
        })),
        ModerationLabels: moderationLabels.map((label) => ({
          Name: label.Name || '',
          Confidence: label.Confidence || 0,
        })),
        TextDetections: textDetections
          .filter((text) => (text.Confidence || 0) > 85)
          .map((text) => ({
            DetectedText: text.DetectedText || '',
            Confidence: text.Confidence || 0,
            Type: (text.Type as 'LINE' | 'WORD') || 'LINE',
          })),
        FaceDetails: faceDetails.map((face) => ({
          Confidence: face.Confidence || 0,
        })),
        Brand: extractBrandFromLabels(labels),
        ConditionHint: extractConditionFromLabels(labels),
      };
    },
    detectLabels,
    detectText,
    detectModerationLabels,
    detectFaces,
  };
}
