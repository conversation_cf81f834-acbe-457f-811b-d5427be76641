/**
 * Type definitions for AWS Rekognition API
 */

// Basic Rekognition Response Types
export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Type: string;
  Confidence: number;
}

export interface RekognitionFaceDetail {
  Confidence: number;
}

// Combined Rekognition API Response
export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

// Configuration Types
export interface RekognitionConfig {
  mock?: boolean;
  credentials?: AWSCredentials;
}

export interface AWSCredentials {
  accessKeyId: string;
  secretAccessKey: string;
  region: string;
}

export interface BedrockConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

// AI Processing Types
export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

export interface AIGeneratedContent {
  itemName: string;
  initialDescription: string;
  refinedDescription: string;
  generationMethod: 'ai' | 'basic' | 'error';
  confidence: 'high' | 'medium' | 'low';
}

export interface ItemGenerationContext {
  labels: string[];
  textDetections: string[];
  brand?: string;
  category?: string;
  condition?: string;
}
