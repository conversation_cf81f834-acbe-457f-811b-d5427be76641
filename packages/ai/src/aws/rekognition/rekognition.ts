/**
 * AWS Rekognition Service for AI-powered image analysis
 * This service provides both mock and real AWS Rekognition functionality
 */

// ===== TYPE DEFINITIONS =====

export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
  ParentName?: string;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Confidence: number;
  Type: 'LINE' | 'WORD';
}

export interface RekognitionFaceDetail {
  BoundingBox: {
    Width: number;
    Height: number;
    Left: number;
    Top: number;
  };
  Confidence: number;
}

export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

export interface AIGeneratedContent {
  itemName: string;
  initialDescription: string;
  refinedDescription: string;
  generationMethod: 'ai' | 'fallback' | 'error';
  confidence: 'high' | 'medium' | 'low';
}

export interface BedrockResponse {
  content: string;
  usage?: {
    inputTokens: number;
    outputTokens: number;
  };
}

// ===== CONFIGURATION =====

export interface RekognitionConfig {
  useMockData?: boolean;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  sessionToken?: string;
  confidenceThreshold?: {
    labels: number;
    text: number;
    moderation: number;
  };
}

export interface BedrockConfig {
  useMockData?: boolean;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  sessionToken?: string;
  modelId?: string;
  maxTokens?: number;
  temperature?: number;
}

// AWS SDK Configuration
export interface AWSCredentials {
  accessKeyId: string;
  secretAccessKey: string;
  sessionToken?: string;
  region: string;
}

const DEFAULT_CONFIG: Required<RekognitionConfig> = {
  useMockData: true,
  region: 'us-east-1',
  accessKeyId: '',
  secretAccessKey: '',
  sessionToken: '',
  confidenceThreshold: {
    labels: 80,
    text: 85,
    moderation: 75,
  },
};

const DEFAULT_BEDROCK_CONFIG: Required<BedrockConfig> = {
  useMockData: true,
  region: 'us-east-1',
  accessKeyId: '',
  secretAccessKey: '',
  sessionToken: '',
  modelId: 'anthropic.claude-3-haiku-20240307-v1:0',
  maxTokens: 1000,
  temperature: 0.7,
};

// ===== MOCK DATA =====

const MOCK_RESPONSES: Record<string, RekognitionResponse> = {
  laptop: {
    Labels: [
      { Name: 'Laptop', Confidence: 95.5 },
      { Name: 'Computer', Confidence: 92.3 },
      { Name: 'Electronics', Confidence: 89.7 },
      { Name: 'Device', Confidence: 87.2 },
      { Name: 'Technology', Confidence: 85.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
      { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
      { DetectedText: '13-inch', Confidence: 91.8, Type: 'WORD' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Good',
  },
  phone: {
    Labels: [
      { Name: 'Mobile Phone', Confidence: 97.8 },
      { Name: 'Smartphone', Confidence: 95.2 },
      { Name: 'Electronics', Confidence: 93.1 },
      { Name: 'Communication Device', Confidence: 88.9 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'iPhone 14', Confidence: 96.7, Type: 'LINE' },
      { DetectedText: 'Pro Max', Confidence: 94.3, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Like New',
  },
  furniture: {
    Labels: [
      { Name: 'Furniture', Confidence: 94.2 },
      { Name: 'Chair', Confidence: 91.5 },
      { Name: 'Seat', Confidence: 88.7 },
      { Name: 'Wood', Confidence: 85.3 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  clothing: {
    Labels: [
      { Name: 'Clothing', Confidence: 96.1 },
      { Name: 'Apparel', Confidence: 93.4 },
      { Name: 'Shirt', Confidence: 89.8 },
      { Name: 'Fashion', Confidence: 87.2 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'Nike', Confidence: 92.5, Type: 'LINE' },
      { DetectedText: 'Size M', Confidence: 88.9, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Nike',
    ConditionHint: 'Good',
  },
  book: {
    Labels: [
      { Name: 'Book', Confidence: 98.5 },
      { Name: 'Publication', Confidence: 95.7 },
      { Name: 'Text', Confidence: 92.3 },
      { Name: 'Literature', Confidence: 89.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'The Great Gatsby', Confidence: 97.2, Type: 'LINE' },
      { DetectedText: 'F. Scott Fitzgerald', Confidence: 94.8, Type: 'LINE' },
    ],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  inappropriate: {
    Labels: [
      { Name: 'Person', Confidence: 95.2 },
      { Name: 'Human', Confidence: 92.8 },
    ],
    ModerationLabels: [
      { Name: 'Suggestive', Confidence: 87.5 },
      { Name: 'Explicit Nudity', Confidence: 82.3 },
    ],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.3, Height: 0.4, Left: 0.2, Top: 0.1 },
        Confidence: 94.7,
      },
    ],
  },
  violence: {
    Labels: [
      { Name: 'Weapon', Confidence: 91.4 },
      { Name: 'Violence', Confidence: 88.7 },
    ],
    ModerationLabels: [
      { Name: 'Violence', Confidence: 91.4 },
      { Name: 'Weapons', Confidence: 88.7 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  drugs: {
    Labels: [
      { Name: 'Drug Paraphernalia', Confidence: 89.3 },
      { Name: 'Substance', Confidence: 85.6 },
    ],
    ModerationLabels: [
      { Name: 'Drugs', Confidence: 89.3 },
      { Name: 'Drug Use', Confidence: 85.6 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  war: {
    Labels: [
      { Name: 'Military', Confidence: 92.1 },
      { Name: 'Weapon', Confidence: 88.4 },
      { Name: 'Combat', Confidence: 85.7 },
    ],
    ModerationLabels: [
      { Name: 'Violence', Confidence: 92.1 },
      { Name: 'Weapons', Confidence: 88.4 },
      { Name: 'Graphic Violence', Confidence: 85.7 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  nudity: {
    Labels: [
      { Name: 'Person', Confidence: 94.8 },
      { Name: 'Human', Confidence: 91.2 },
    ],
    ModerationLabels: [
      { Name: 'Explicit Nudity', Confidence: 94.8 },
      { Name: 'Graphic Male Nudity', Confidence: 91.2 },
      { Name: 'Graphic Female Nudity', Confidence: 88.5 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  zeroAnalysis: {
    Labels: [],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Unknown',
  },
  withFaces: {
    Labels: [
      { Name: 'Person', Confidence: 96.8 },
      { Name: 'Human', Confidence: 94.2 },
      { Name: 'Face', Confidence: 91.5 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.25, Height: 0.35, Left: 0.3, Top: 0.2 },
        Confidence: 96.8,
      },
      {
        BoundingBox: { Width: 0.22, Height: 0.32, Left: 0.6, Top: 0.25 },
        Confidence: 94.2,
      },
    ],
  },
};

// ===== CORE FUNCTIONS =====

/**
 * Analyzes an image using AWS Rekognition (or mock data)
 */
export async function analyzeImageWithRekognition(
  imageFile: File,
  config: RekognitionConfig = {}
): Promise<RekognitionResponse> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  if (finalConfig.useMockData) {
    return analyzeMockImage(imageFile);
  }

  return analyzeImageWithAWS(imageFile, finalConfig);
}

/**
 * Analyzes image using real AWS Rekognition API
 */
async function analyzeImageWithAWS(
  imageFile: File,
  config: Required<RekognitionConfig>
): Promise<RekognitionResponse> {
  // Import AWS SDK dynamically to avoid issues in browser environments
  const {
    RekognitionClient,
    DetectLabelsCommand,
    DetectTextCommand,
    DetectModerationLabelsCommand,
    DetectFacesCommand,
  } = await import('@aws-sdk/client-rekognition');

  // Create Rekognition client
  const client = new RekognitionClient({
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
      ...(config.sessionToken && { sessionToken: config.sessionToken }),
    },
  });

  // Convert File to bytes
  const imageBytes = await fileToBytes(imageFile);

  try {
    // Run all detection operations in parallel
    const [labelsResult, textResult, moderationResult, facesResult] =
      await Promise.allSettled([
        client.send(
          new DetectLabelsCommand({
            Image: { Bytes: imageBytes },
            MaxLabels: 20,
            MinConfidence: config.confidenceThreshold.labels,
          })
        ),
        client.send(
          new DetectTextCommand({
            Image: { Bytes: imageBytes },
          })
        ),
        client.send(
          new DetectModerationLabelsCommand({
            Image: { Bytes: imageBytes },
            MinConfidence: config.confidenceThreshold.moderation,
          })
        ),
        client.send(
          new DetectFacesCommand({
            Image: { Bytes: imageBytes },
            Attributes: ['DEFAULT'],
          })
        ),
      ]);

    // Process results
    const labels =
      labelsResult.status === 'fulfilled'
        ? labelsResult.value.Labels || []
        : [];
    const textDetections =
      textResult.status === 'fulfilled'
        ? textResult.value.TextDetections || []
        : [];
    const moderationLabels =
      moderationResult.status === 'fulfilled'
        ? moderationResult.value.ModerationLabels || []
        : [];
    const faceDetails =
      facesResult.status === 'fulfilled'
        ? facesResult.value.FaceDetails || []
        : [];

    // Convert AWS SDK response to our format
    return {
      Labels: labels.map((label) => ({
        Name: label.Name || '',
        Confidence: label.Confidence || 0,
      })),
      ModerationLabels: moderationLabels.map((label) => ({
        Name: label.Name || '',
        Confidence: label.Confidence || 0,
        ParentName: label.ParentName,
      })),
      TextDetections: textDetections
        .filter(
          (text) => (text.Confidence || 0) > config.confidenceThreshold.text
        )
        .map((text) => ({
          DetectedText: text.DetectedText || '',
          Confidence: text.Confidence || 0,
          Type: (text.Type as 'LINE' | 'WORD') || 'LINE',
        })),
      FaceDetails: faceDetails.map((face) => ({
        BoundingBox: {
          Width: face.BoundingBox?.Width || 0,
          Height: face.BoundingBox?.Height || 0,
          Left: face.BoundingBox?.Left || 0,
          Top: face.BoundingBox?.Top || 0,
        },
        Confidence: face.Confidence || 0,
      })),
      Brand: extractBrandFromLabels(labels),
      ConditionHint: extractConditionFromLabels(labels),
    };
  } catch (error) {
    console.error('AWS Rekognition error:', error);
    throw new Error(`AWS Rekognition analysis failed: ${error}`);
  }
}

/**
 * Mock image analysis for development and testing
 */
async function analyzeMockImage(imageFile: File): Promise<RekognitionResponse> {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1500 + Math.random() * 1000)
  );

  // Determine mock response based on filename or random selection
  const fileName = imageFile.name.toLowerCase();

  if (
    fileName.includes('laptop') ||
    fileName.includes('macbook') ||
    fileName.includes('computer')
  ) {
    return MOCK_RESPONSES['laptop'];
  }
  if (
    fileName.includes('phone') ||
    fileName.includes('iphone') ||
    fileName.includes('mobile')
  ) {
    return MOCK_RESPONSES['phone'];
  }
  if (
    fileName.includes('chair') ||
    fileName.includes('furniture') ||
    fileName.includes('desk')
  ) {
    return MOCK_RESPONSES['furniture'];
  }
  if (
    fileName.includes('shirt') ||
    fileName.includes('clothing') ||
    fileName.includes('apparel')
  ) {
    return MOCK_RESPONSES['clothing'];
  }
  if (fileName.includes('book') || fileName.includes('novel')) {
    return MOCK_RESPONSES['book'];
  }
  if (fileName.includes('inappropriate') || fileName.includes('nsfw')) {
    return MOCK_RESPONSES['inappropriate'];
  }
  if (
    fileName.includes('face') ||
    fileName.includes('person') ||
    fileName.includes('people')
  ) {
    return MOCK_RESPONSES['withFaces'];
  }

  // Random selection for generic images with edge cases
  const random = Math.random();

  // 18% chance of moderated content (various types) - BLOCKS LISTING
  if (random < 0.18) {
    const moderatedTypes = [
      'inappropriate',
      'violence',
      'drugs',
      'war',
      'nudity',
    ];
    const randomType =
      moderatedTypes[Math.floor(Math.random() * moderatedTypes.length)];
    return MOCK_RESPONSES[randomType as keyof typeof MOCK_RESPONSES];
  }

  // 10% chance of zero analysis - NO DATA DETECTED
  if (random < 0.28) {
    return MOCK_RESPONSES['zeroAnalysis'];
  }

  // 7% chance of face detection
  if (random < 0.35) {
    return MOCK_RESPONSES['withFaces'];
  }

  // 10% chance of unrecognized/poor quality image
  if (random < 0.45) {
    return {
      Labels: [
        { Name: 'Object', Confidence: 45.2 },
        { Name: 'Item', Confidence: 38.7 },
        { Name: 'Unidentified', Confidence: 32.1 },
      ],
      ModerationLabels: [],
      TextDetections: [],
      FaceDetails: [],
      ConditionHint: 'Unknown',
    };
  }

  // Regular responses for remaining 55%
  const validResponses = [
    MOCK_RESPONSES['laptop'],
    MOCK_RESPONSES['phone'],
    MOCK_RESPONSES['furniture'],
    MOCK_RESPONSES['clothing'],
    MOCK_RESPONSES['book'],
  ];
  const randomIndex = Math.floor(Math.random() * validResponses.length);
  return validResponses[randomIndex];
}

/**
 * Processes raw Rekognition response into structured AI result
 * Enhanced with new AI-powered features
 */
export async function processRekognitionResponse(
  response: RekognitionResponse,
  bedrockConfig: BedrockConfig = {}
): Promise<ProcessedAIResult> {
  // Extract top labels with confidence above threshold
  const topLabels = response.Labels.filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 5);

  // Extract text detections
  const textDetections = response.TextDetections.filter(
    (text) => text.Confidence > 85 && text.Type === 'LINE'
  )
    .map((text) => text.DetectedText)
    .slice(0, 3);

  // Check for content moderation issues
  const contentWarning = checkContentModeration(response.ModerationLabels);

  // Check for face detection
  const faceWarning = checkFaceDetection(response.FaceDetails);

  // Check for different analysis scenarios
  const isZeroAnalysis = topLabels.length === 0 && textDetections.length === 0;
  const isUnrecognized =
    topLabels.includes('Object') && topLabels.includes('Unidentified');

  // Generate AI-powered item name and description
  let itemName = '';
  let description = '';

  if (isZeroAnalysis) {
    itemName = 'No Data Detected';
    description =
      'No analyzable data was detected in this image. This could be due to poor lighting, blur, or an empty/unclear photo. Please try uploading a clearer image or fill in the details manually.';
  } else if (isUnrecognized) {
    itemName = 'Unrecognized Item';
    description =
      'AI could not clearly identify this item from the image. Please manually enter the item details below for the best listing results.';
  } else {
    // Use new AI-powered features
    try {
      itemName = await generateItemName(
        response.Labels,
        response.TextDetections,
        response.Brand,
        bedrockConfig
      );

      const initialDescription = await generateInitialDescription(
        response.Labels,
        response.TextDetections,
        response.Brand,
        bedrockConfig
      );

      // Determine category and condition for refinement
      const category = determineCategory(topLabels);
      const condition = response.ConditionHint || determineCondition(topLabels);

      description = await refineDescriptionWithBedrock(
        initialDescription,
        itemName,
        category,
        condition,
        bedrockConfig
      );
    } catch (error) {
      console.error(
        'AI content generation failed, falling back to basic generation:',
        error
      );

      // Fallback to basic generation
      if (textDetections.length > 0) {
        itemName = textDetections[0];
      } else if (topLabels.length > 0) {
        itemName = topLabels[0];
      } else {
        itemName = 'Item';
      }

      description = await generateBedrockDescription(
        topLabels,
        textDetections,
        response.Brand
      );
    }
  }

  // Determine category from labels
  const category = determineCategory(topLabels);

  // Determine condition from hints or labels
  const condition = response.ConditionHint || determineCondition(topLabels);

  return {
    itemName,
    description,
    category,
    condition,
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

// ===== HELPER FUNCTIONS =====

/**
 * Checks for content moderation issues
 */
function checkContentModeration(
  moderationLabels: RekognitionModerationLabel[]
): string | undefined {
  if (moderationLabels.length === 0) return undefined;

  const highConfidenceLabels = moderationLabels.filter(
    (label) => label.Confidence > 75
  );

  if (highConfidenceLabels.length === 0) return undefined;

  // Categorize moderation issues
  const nudityLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('nudity')
  );
  const violenceLabels = highConfidenceLabels.filter(
    (label) =>
      label.Name.toLowerCase().includes('violence') ||
      label.Name.toLowerCase().includes('weapon')
  );
  const drugLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('drug')
  );
  const suggestiveLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('suggestive')
  );

  if (nudityLabels.length > 0) {
    return 'This image contains nudity or explicit content and cannot be used for marketplace listings. Please upload a different image that complies with our community guidelines.';
  }

  if (violenceLabels.length > 0) {
    return 'This image contains violent content, weapons, or war-related material and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (drugLabels.length > 0) {
    return 'This image contains drug-related content and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (suggestiveLabels.length > 0) {
    return 'This image contains inappropriate or suggestive content and cannot be used for marketplace listings. Please upload a different image.';
  }

  // Generic moderation warning
  return `This image contains content that violates our community guidelines (${highConfidenceLabels[0].Name}) and cannot be used for marketplace listings.`;
}

/**
 * Checks for face detection and privacy concerns
 */
function checkFaceDetection(
  faceDetails: RekognitionFaceDetail[]
): string | undefined {
  if (faceDetails.length === 0) return undefined;

  const highConfidenceFaces = faceDetails.filter(
    (face) => face.Confidence > 90
  );

  if (highConfidenceFaces.length === 0) return undefined;

  if (highConfidenceFaces.length === 1) {
    return 'We detected a face in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.';
  }

  return `We detected ${highConfidenceFaces.length} faces in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.`;
}

/**
 * Determines item category from detected labels
 */
function determineCategory(labels: string[]): string {
  const categoryMappings: Record<string, string[]> = {
    Electronics: [
      'Laptop',
      'Computer',
      'Mobile Phone',
      'Smartphone',
      'Electronics',
      'Device',
      'Technology',
    ],
    Furniture: ['Furniture', 'Chair', 'Table', 'Desk', 'Seat', 'Wood'],
    Clothing: ['Clothing', 'Apparel', 'Shirt', 'Fashion', 'Garment'],
    Books: ['Book', 'Publication', 'Text', 'Literature', 'Novel'],
    Sports: ['Sports', 'Equipment', 'Ball', 'Fitness', 'Exercise'],
    Toys: ['Toy', 'Game', 'Doll', 'Puzzle', 'Entertainment'],
    Home: ['Home', 'Kitchen', 'Appliance', 'Tool', 'Utensil'],
    Automotive: ['Car', 'Vehicle', 'Automotive', 'Transportation'],
  };

  for (const [category, keywords] of Object.entries(categoryMappings)) {
    if (labels.some((label) => keywords.includes(label))) {
      return category;
    }
  }

  return 'Other';
}

/**
 * Determines item condition from labels
 */
function determineCondition(labels: string[]): string {
  // Look for condition-related keywords in labels
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect'],
    Good: ['good', 'clean', 'working', 'functional'],
    Fair: ['used', 'worn', 'old', 'vintage'],
    Poor: ['damaged', 'broken', 'cracked', 'torn'],
  };

  for (const [condition, keywords] of Object.entries(conditionKeywords)) {
    if (
      labels.some((label) =>
        keywords.some((keyword) =>
          label.toLowerCase().includes(keyword.toLowerCase())
        )
      )
    ) {
      return condition;
    }
  }

  // Default condition based on item type
  if (labels.includes('Electronics') || labels.includes('Technology')) {
    return 'Good';
  }

  return 'Good'; // Default fallback
}

/**
 * Converts File to Uint8Array for AWS SDK
 * Works in both browser and Node.js environments
 */
async function fileToBytes(file: File): Promise<Uint8Array> {
  // Check if we're in a browser environment
  if (typeof FileReader !== 'undefined') {
    // Browser environment - use FileReader
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(new Uint8Array(reader.result));
        } else {
          reject(new Error('Failed to read file as ArrayBuffer'));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(file);
    });
  } else {
    // Node.js environment - use arrayBuffer method
    try {
      const arrayBuffer = await file.arrayBuffer();
      return new Uint8Array(arrayBuffer);
    } catch (error) {
      throw new Error(`Failed to convert file to bytes: ${error}`);
    }
  }
}

/**
 * Extracts brand information from AWS labels
 */
function extractBrandFromLabels(labels: any[]): string | undefined {
  // Look for common brand indicators in labels
  const brandKeywords = [
    'Apple',
    'Samsung',
    'Nike',
    'Adidas',
    'Sony',
    'Microsoft',
    'Google',
    'Amazon',
    'Dell',
    'HP',
    'Lenovo',
    'Canon',
    'Nikon',
  ];

  for (const label of labels) {
    const labelName = label.Name || '';
    for (const brand of brandKeywords) {
      if (labelName.toLowerCase().includes(brand.toLowerCase())) {
        return brand;
      }
    }
  }

  return undefined;
}

/**
 * Extracts condition hints from AWS labels
 */
function extractConditionFromLabels(labels: any[]): string | undefined {
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect', 'unused'],
    Good: ['good', 'clean', 'working', 'functional', 'excellent'],
    Fair: ['used', 'worn', 'old', 'vintage', 'scratched'],
    Poor: ['damaged', 'broken', 'cracked', 'torn', 'defective'],
  };

  for (const label of labels) {
    const labelName = (label.Name || '').toLowerCase();
    for (const [condition, keywords] of Object.entries(conditionKeywords)) {
      if (keywords.some((keyword) => labelName.includes(keyword))) {
        return condition;
      }
    }
  }

  return undefined;
}

/**
 * Generates AI-powered description using mock Bedrock functionality
 */
async function generateBedrockDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  // Simulate Bedrock API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 500 + Math.random() * 500)
  );

  // Build description based on detected elements
  let description = '';

  // Start with the main item
  if (labels.length > 0) {
    const mainItem = labels[0];
    description = `This ${mainItem.toLowerCase()}`;

    // Add brand if detected
    if (brand) {
      description += ` by ${brand}`;
    }

    // Add model/text information if available
    if (textDetections.length > 0) {
      const modelInfo = textDetections.join(', ');
      description += ` (${modelInfo})`;
    }

    // Add descriptive details based on other labels
    const additionalLabels = labels.slice(1, 3);
    if (additionalLabels.length > 0) {
      description += ` features ${additionalLabels
        .map((label) => label.toLowerCase())
        .join(' and ')} characteristics`;
    }

    // Add condition-based description
    description +=
      '. This item appears to be in good condition based on the image analysis';

    // Add call to action
    description +=
      '. Please review the details and condition before making your purchase decision.';
  } else {
    description =
      'Item details could not be automatically determined from the image. Please provide a detailed description manually.';
  }

  return description;
}

// ===== AI HELPER FUNCTIONS =====

/**
 * Generates a mock item name for development/testing
 */
function generateMockItemName(
  labels: string[],
  textDetections: string[],
  brand?: string
): string {
  // Use detected text first if available
  if (textDetections.length > 0) {
    const text = textDetections[0];
    if (brand && !text.toLowerCase().includes(brand.toLowerCase())) {
      return `${brand} ${text}`;
    }
    return text;
  }

  // Use labels to create a name
  if (labels.length > 0) {
    const mainItem = labels[0];
    if (brand) {
      return `${brand} ${mainItem}`;
    }
    return mainItem;
  }

  return 'Unidentified Item';
}

/**
 * Generates a mock initial description for development/testing
 */
function generateMockInitialDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): string {
  if (labels.length === 0) {
    return 'Item details could not be determined from the image. Please provide a detailed description manually.';
  }

  const mainItem = labels[0];
  let description = `This ${mainItem.toLowerCase()}`;

  if (brand) {
    description += ` by ${brand}`;
  }

  if (textDetections.length > 0) {
    const modelInfo = textDetections.join(', ');
    description += ` (${modelInfo})`;
  }

  const additionalLabels = labels.slice(1, 3);
  if (additionalLabels.length > 0) {
    description += ` features ${additionalLabels
      .map((label) => label.toLowerCase())
      .join(' and ')} characteristics`;
  }

  description +=
    '. This item appears to be in good condition based on the image analysis.';

  return description;
}

/**
 * Refines a description using mock enhancement for development/testing
 */
function refineMockDescription(
  initialDescription: string,
  _itemName: string,
  category: string,
  condition: string
): string {
  // Add marketplace-specific enhancements
  let refined = initialDescription;

  // Add category-specific details
  const categoryEnhancements: Record<string, string> = {
    Electronics:
      'Perfect for tech enthusiasts looking for reliable electronics.',
    Furniture: 'A great addition to any home or office space.',
    Clothing: 'Stylish and comfortable, perfect for everyday wear.',
    Books: 'A must-read for book lovers and collectors.',
    Sports: 'Ideal for fitness enthusiasts and sports lovers.',
    Toys: 'Great for children and collectors alike.',
    Home: 'Perfect for organizing and enhancing your living space.',
    Automotive: 'Essential for vehicle maintenance and enhancement.',
  };

  if (categoryEnhancements[category]) {
    refined += ` ${categoryEnhancements[category]}`;
  }

  // Add condition-specific notes
  const conditionNotes: Record<string, string> = {
    'Like New': 'Item is in excellent condition with minimal signs of use.',
    Good: 'Item shows normal wear but remains fully functional.',
    Fair: 'Item has visible wear but is still in working condition.',
    Poor: 'Item shows significant wear and may require attention.',
  };

  if (conditionNotes[condition]) {
    refined += ` ${conditionNotes[condition]}`;
  }

  refined += ' Please review all details and photos before purchasing.';

  return refined;
}

/**
 * Determines confidence level for AI generation
 */
function determineGenerationConfidence(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string
): 'high' | 'medium' | 'low' {
  const highConfidenceLabels = labels.filter((label) => label.Confidence > 90);
  const highConfidenceText = textDetections.filter(
    (text) => text.Confidence > 90
  );

  if (
    highConfidenceLabels.length >= 3 &&
    (highConfidenceText.length > 0 || brand)
  ) {
    return 'high';
  }

  if (highConfidenceLabels.length >= 2 || highConfidenceText.length > 0) {
    return 'medium';
  }

  return 'low';
}

/**
 * Generates item name using real AWS Bedrock API
 */
async function generateItemNameWithBedrock(
  labels: string[],
  textDetections: string[],
  brand: string | undefined,
  config: Required<BedrockConfig>
): Promise<string> {
  const prompt = createItemNamePrompt(labels, textDetections, brand);
  const response = await callBedrockAPI(prompt, config);
  return response.content.trim();
}

/**
 * Generates initial description using real AWS Bedrock API
 */
async function generateInitialDescriptionWithBedrock(
  labels: string[],
  textDetections: string[],
  brand: string | undefined,
  config: Required<BedrockConfig>
): Promise<string> {
  const prompt = createInitialDescriptionPrompt(labels, textDetections, brand);
  const response = await callBedrockAPI(prompt, config);
  return response.content.trim();
}

/**
 * Refines description using real AWS Bedrock API
 */
async function refineDescriptionWithBedrockAPI(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string,
  config: Required<BedrockConfig>
): Promise<string> {
  const prompt = createRefinementPrompt(
    initialDescription,
    itemName,
    category,
    condition
  );
  const response = await callBedrockAPI(prompt, config);
  return response.content.trim();
}

/**
 * Calls AWS Bedrock API with the given prompt
 */
async function callBedrockAPI(
  prompt: string,
  config: Required<BedrockConfig>
): Promise<BedrockResponse> {
  // Import AWS SDK dynamically to avoid issues in browser environments
  const { BedrockRuntimeClient, InvokeModelCommand } = await import(
    '@aws-sdk/client-bedrock-runtime'
  );

  const client = new BedrockRuntimeClient({
    region: config.region,
    credentials: {
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
      ...(config.sessionToken && { sessionToken: config.sessionToken }),
    },
  });

  const requestBody = {
    anthropic_version: 'bedrock-2023-05-31',
    max_tokens: config.maxTokens,
    temperature: config.temperature,
    messages: [
      {
        role: 'user',
        content: prompt,
      },
    ],
  };

  try {
    const command = new InvokeModelCommand({
      modelId: config.modelId,
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify(requestBody),
    });

    const response = await client.send(command);
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));

    return {
      content: responseBody.content[0].text,
      usage: {
        inputTokens: responseBody.usage?.input_tokens || 0,
        outputTokens: responseBody.usage?.output_tokens || 0,
      },
    };
  } catch (error) {
    console.error('AWS Bedrock API error:', error);
    throw new Error(`AWS Bedrock API call failed: ${error}`);
  }
}

/**
 * Creates a prompt for generating item names
 */
function createItemNamePrompt(
  labels: string[],
  textDetections: string[],
  brand?: string
): string {
  let prompt = `You are an AI assistant helping create concise, marketable product names for marketplace listings.

Based on the following image analysis data, generate a short, clear product name (maximum 6 words):

Detected Labels: ${labels.join(', ')}`;

  if (textDetections.length > 0) {
    prompt += `\nDetected Text: ${textDetections.join(', ')}`;
  }

  if (brand) {
    prompt += `\nDetected Brand: ${brand}`;
  }

  prompt += `

Guidelines:
- Keep it under 6 words
- Make it marketable and appealing
- Include brand name if detected
- Use the most specific/relevant detected information
- Avoid generic terms like "object" or "item"

Generate only the product name, no additional text:`;

  return prompt;
}

/**
 * Creates a prompt for generating initial descriptions
 */
function createInitialDescriptionPrompt(
  labels: string[],
  textDetections: string[],
  brand?: string
): string {
  let prompt = `You are an AI assistant helping create product descriptions for marketplace listings.

Based on the following image analysis data, generate a clear, informative product description (2-3 sentences):

Detected Labels: ${labels.join(', ')}`;

  if (textDetections.length > 0) {
    prompt += `\nDetected Text: ${textDetections.join(', ')}`;
  }

  if (brand) {
    prompt += `\nDetected Brand: ${brand}`;
  }

  prompt += `

Guidelines:
- Write 2-3 clear, informative sentences
- Focus on key features and characteristics
- Include brand and model information if available
- Be factual and avoid exaggerated claims
- Make it suitable for a marketplace listing

Generate only the description, no additional text:`;

  return prompt;
}

/**
 * Creates a prompt for refining descriptions with Bedrock
 */
function createRefinementPrompt(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string
): string {
  return `You are an AI assistant helping refine product descriptions for marketplace listings.

Please enhance the following product description to make it more compelling and marketplace-ready:

Item Name: ${itemName}
Category: ${category}
Condition: ${condition}
Initial Description: ${initialDescription}

Guidelines:
- Keep the core information accurate
- Make it more engaging and marketable
- Add relevant selling points for the category
- Include appropriate condition details
- Maintain a professional, trustworthy tone
- Keep it concise (3-4 sentences maximum)
- End with a call to action about reviewing details

Generate only the refined description, no additional text:`;
}

// ===== NEW AI-POWERED FEATURES =====

/**
 * Auto-generates a concise, marketable product name based on detected labels
 */
export async function generateItemName(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  // Filter high-confidence labels
  const topLabels = labels
    .filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 3);

  // Extract meaningful text
  const meaningfulText = textDetections
    .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
    .map((text) => text.DetectedText)
    .slice(0, 2);

  // Check for edge cases
  if (topLabels.length === 0 && meaningfulText.length === 0) {
    return 'Unidentified Item';
  }

  if (finalConfig.useMockData) {
    return generateMockItemName(topLabels, meaningfulText, brand);
  }

  return generateItemNameWithBedrock(
    topLabels,
    meaningfulText,
    brand,
    finalConfig
  );
}

/**
 * Auto-generates an initial description using detected labels and text
 */
export async function generateInitialDescription(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  const topLabels = labels
    .filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 5);

  const textInfo = textDetections
    .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
    .map((text) => text.DetectedText)
    .slice(0, 3);

  if (finalConfig.useMockData) {
    return generateMockInitialDescription(topLabels, textInfo, brand);
  }

  return generateInitialDescriptionWithBedrock(
    topLabels,
    textInfo,
    brand,
    finalConfig
  );
}

/**
 * Refines and enhances a description using AWS Bedrock
 */
export async function refineDescriptionWithBedrock(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string,
  config: BedrockConfig = {}
): Promise<string> {
  const finalConfig = { ...DEFAULT_BEDROCK_CONFIG, ...config };

  if (finalConfig.useMockData) {
    return refineMockDescription(
      initialDescription,
      itemName,
      category,
      condition
    );
  }

  return refineDescriptionWithBedrockAPI(
    initialDescription,
    itemName,
    category,
    condition,
    finalConfig
  );
}

/**
 * Complete AI-powered content generation workflow
 */
export async function generateAIContent(
  rekognitionResponse: RekognitionResponse,
  config: BedrockConfig = {}
): Promise<AIGeneratedContent> {
  try {
    // Generate item name
    const itemName = await generateItemName(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand,
      config
    );

    // Generate initial description
    const initialDescription = await generateInitialDescription(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand,
      config
    );

    // Determine category and condition for refinement
    const topLabels = rekognitionResponse.Labels.filter(
      (label) => label.Confidence > 80
    ).map((label) => label.Name);

    const category = determineCategory(topLabels);
    const condition =
      rekognitionResponse.ConditionHint || determineCondition(topLabels);

    // Refine description with Bedrock
    const refinedDescription = await refineDescriptionWithBedrock(
      initialDescription,
      itemName,
      category,
      condition,
      config
    );

    // Determine confidence based on available data
    const confidence = determineGenerationConfidence(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand
    );

    return {
      itemName,
      initialDescription,
      refinedDescription,
      generationMethod: 'ai',
      confidence,
    };
  } catch (error) {
    console.error('AI content generation error:', error);

    // Fallback to basic generation
    const fallbackName =
      rekognitionResponse.Labels.length > 0
        ? rekognitionResponse.Labels[0].Name
        : 'Item';

    const fallbackDescription =
      'Item details could not be automatically generated. Please provide a detailed description manually.';

    return {
      itemName: fallbackName,
      initialDescription: fallbackDescription,
      refinedDescription: fallbackDescription,
      generationMethod: 'error',
      confidence: 'low',
    };
  }
}

// ===== EXPORTS =====

export default {
  analyzeImageWithRekognition,
  processRekognitionResponse,
  generateItemName,
  generateInitialDescription,
  refineDescriptionWithBedrock,
  generateAIContent,
};
