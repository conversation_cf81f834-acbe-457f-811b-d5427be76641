/**
 * AWS Rekognition Service for AI-powered image analysis
 * This service provides both mock and real AWS Rekognition functionality
 */

// Import types
import {
  AIGeneratedContent,
  BedrockConfig,
  ItemGenerationContext,
  ProcessedAIResult,
  RekognitionConfig,
  RekognitionLabel,
  RekognitionResponse,
  RekognitionTextDetection,
} from './types';

// Import helpers
import {
  checkContentModeration,
  checkFaceDetection,
  determineCategory,
  determineCondition,
  determineGenerationConfidence,
  extractBrandFromLabels,
  extractConditionFromLabels,
} from './helper';

// Import utilities
import { fileToBytes, generateBasicDescription } from './utils';

// Import mock data
import { MOCK_RESPONSES } from './mock-data';

// Import Bedrock integration functions
import {
  bedrockGenerateInitialDescription,
  bedrockGenerateItemName,
  bedrockRefineDescription,
} from './bedrock';

// Default configuration
const DEFAULT_CONFIG: Required<RekognitionConfig> = {
  mock: true,
  credentials: {
    accessKeyId: '',
    secretAccessKey: '',
    region: 'us-east-1',
  },
};

// ===== CORE FUNCTIONS =====

/**
 * Analyzes an image using AWS Rekognition (or mock data)
 */
export async function analyzeImageWithRekognition(
  imageFile: File,
  config: RekognitionConfig = {}
): Promise<RekognitionResponse> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  if (finalConfig.mock) {
    return analyzeMockImage(imageFile);
  }

  return analyzeImageWithAWS(imageFile, finalConfig);
}

/**
 * Analyzes image using real AWS Rekognition API
 */
async function analyzeImageWithAWS(
  imageFile: File,
  config: RekognitionConfig
): Promise<RekognitionResponse> {
  // Import AWS SDK dynamically to avoid issues in browser environments
  const {
    RekognitionClient,
    DetectLabelsCommand,
    DetectTextCommand,
    DetectModerationLabelsCommand,
    DetectFacesCommand,
  } = await import('@aws-sdk/client-rekognition');

  // Create Rekognition client
  const client = new RekognitionClient({
    region: config.credentials?.region || 'us-east-1',
    credentials: {
      accessKeyId: config.credentials?.accessKeyId || '',
      secretAccessKey: config.credentials?.secretAccessKey || '',
    },
  });

  // Convert File to bytes
  const imageBytes = await fileToBytes(imageFile);

  try {
    // Run all detection operations in parallel
    const [labelsResult, textResult, moderationResult, facesResult] =
      await Promise.allSettled([
        client.send(
          new DetectLabelsCommand({
            Image: { Bytes: imageBytes },
            MaxLabels: 20,
            MinConfidence: 80,
          })
        ),
        client.send(
          new DetectTextCommand({
            Image: { Bytes: imageBytes },
          })
        ),
        client.send(
          new DetectModerationLabelsCommand({
            Image: { Bytes: imageBytes },
            MinConfidence: 75,
          })
        ),
        client.send(
          new DetectFacesCommand({
            Image: { Bytes: imageBytes },
            Attributes: ['DEFAULT'],
          })
        ),
      ]);

    // Process results
    const labels =
      labelsResult.status === 'fulfilled'
        ? labelsResult.value.Labels || []
        : [];
    const textDetections =
      textResult.status === 'fulfilled'
        ? textResult.value.TextDetections || []
        : [];
    const moderationLabels =
      moderationResult.status === 'fulfilled'
        ? moderationResult.value.ModerationLabels || []
        : [];
    const faceDetails =
      facesResult.status === 'fulfilled'
        ? facesResult.value.FaceDetails || []
        : [];

    // Convert AWS SDK response to our format
    return {
      Labels: labels.map((label) => ({
        Name: label.Name || '',
        Confidence: label.Confidence || 0,
      })),
      ModerationLabels: moderationLabels.map((label) => ({
        Name: label.Name || '',
        Confidence: label.Confidence || 0,
      })),
      TextDetections: textDetections
        .filter((text) => (text.Confidence || 0) > 85)
        .map((text) => ({
          DetectedText: text.DetectedText || '',
          Confidence: text.Confidence || 0,
          Type: (text.Type as 'LINE' | 'WORD') || 'LINE',
        })),
      FaceDetails: faceDetails.map((face) => ({
        Confidence: face.Confidence || 0,
      })),
      Brand: extractBrandFromLabels(labels),
      ConditionHint: extractConditionFromLabels(labels),
    };
  } catch (error) {
    console.error('AWS Rekognition error:', error);
    throw new Error(`AWS Rekognition analysis failed: ${error}`);
  }
}

/**
 * Mock image analysis for development and testing
 */
async function analyzeMockImage(imageFile: File): Promise<RekognitionResponse> {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1500 + Math.random() * 1000)
  );

  // Determine mock response based on filename or random selection
  const fileName = imageFile.name.toLowerCase();

  if (
    fileName.includes('laptop') ||
    fileName.includes('macbook') ||
    fileName.includes('computer')
  ) {
    return MOCK_RESPONSES['laptop'];
  }
  if (
    fileName.includes('phone') ||
    fileName.includes('iphone') ||
    fileName.includes('mobile')
  ) {
    return MOCK_RESPONSES['phone'];
  }
  if (
    fileName.includes('chair') ||
    fileName.includes('furniture') ||
    fileName.includes('desk')
  ) {
    return MOCK_RESPONSES['furniture'];
  }
  if (
    fileName.includes('shirt') ||
    fileName.includes('clothing') ||
    fileName.includes('apparel')
  ) {
    return MOCK_RESPONSES['clothing'];
  }
  if (fileName.includes('book') || fileName.includes('novel')) {
    return MOCK_RESPONSES['book'];
  }
  if (fileName.includes('inappropriate') || fileName.includes('nsfw')) {
    return MOCK_RESPONSES['inappropriate'];
  }
  if (
    fileName.includes('face') ||
    fileName.includes('person') ||
    fileName.includes('people')
  ) {
    return MOCK_RESPONSES['face'];
  }

  // Random selection for generic images with edge cases
  const random = Math.random();

  // 18% chance of moderated content (various types) - BLOCKS LISTING
  if (random < 0.18) {
    const moderatedTypes = ['inappropriate'];
    const randomType =
      moderatedTypes[Math.floor(Math.random() * moderatedTypes.length)];
    return MOCK_RESPONSES[randomType];
  }

  // 10% chance of zero analysis - NO DATA DETECTED
  if (random < 0.28) {
    return {
      Labels: [],
      ModerationLabels: [],
      TextDetections: [],
      FaceDetails: [],
      ConditionHint: 'Unknown',
    };
  }

  // 7% chance of face detection
  if (random < 0.35) {
    return MOCK_RESPONSES['face'];
  }

  // 10% chance of unrecognized/poor quality image
  if (random < 0.45) {
    return {
      Labels: [
        { Name: 'Object', Confidence: 45.2 },
        { Name: 'Item', Confidence: 38.7 },
        { Name: 'Unidentified', Confidence: 32.1 },
      ],
      ModerationLabels: [],
      TextDetections: [],
      FaceDetails: [],
      ConditionHint: 'Unknown',
    };
  }

  // Regular responses for remaining 55%
  const validResponses = [
    MOCK_RESPONSES['laptop'],
    MOCK_RESPONSES['phone'],
    MOCK_RESPONSES['furniture'],
    MOCK_RESPONSES['clothing'],
    MOCK_RESPONSES['book'],
  ];
  const randomIndex = Math.floor(Math.random() * validResponses.length);
  return validResponses[randomIndex];
}

/**
 * Processes raw Rekognition response into structured AI result
 * Enhanced with new AI-powered features
 */
export async function processRekognitionResponse(
  response: RekognitionResponse,
  bedrockConfig: BedrockConfig = {}
): Promise<ProcessedAIResult> {
  // Extract top labels with confidence above threshold
  const topLabels = response.Labels.filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 5);

  // Extract text detections
  const textDetections = response.TextDetections.filter(
    (text) => text.Confidence > 85 && text.Type === 'LINE'
  )
    .map((text) => text.DetectedText)
    .slice(0, 3);

  // Check for content moderation issues
  const contentWarning = checkContentModeration(response.ModerationLabels);

  // Check for face detection
  const faceWarning = checkFaceDetection(response.FaceDetails);

  // Check for different analysis scenarios
  const isZeroAnalysis = topLabels.length === 0 && textDetections.length === 0;
  const isUnrecognized =
    topLabels.includes('Object') && topLabels.includes('Unidentified');

  // Generate AI-powered item name and description
  let itemName = '';
  let description = '';

  if (isZeroAnalysis) {
    itemName = 'No Data Detected';
    description =
      'No analyzable data was detected in this image. This could be due to poor lighting, blur, or an empty/unclear photo. Please try uploading a clearer image or fill in the details manually.';
  } else if (isUnrecognized) {
    itemName = 'Unrecognized Item';
    description =
      'AI could not clearly identify this item from the image. Please manually enter the item details below for the best listing results.';
  } else {
    // Use new AI-powered features
    try {
      itemName = await generateItemName(
        response.Labels,
        response.TextDetections,
        response.Brand,
        bedrockConfig
      );

      const initialDescription = await generateInitialDescription(
        response.Labels,
        response.TextDetections,
        response.Brand,
        bedrockConfig
      );

      // Determine category and condition for refinement
      const category = determineCategory(topLabels);
      const condition = response.ConditionHint || determineCondition(topLabels);

      description = await refineDescriptionWithBedrock(
        initialDescription,
        itemName,
        category,
        condition,
        bedrockConfig
      );
    } catch (error) {
      console.error(
        'AI content generation failed, falling back to basic generation:',
        error
      );

      // Fallback to basic generation
      if (textDetections.length > 0) {
        itemName = textDetections[0];
      } else if (topLabels.length > 0) {
        itemName = topLabels[0];
      } else {
        itemName = 'Item';
      }

      description = await generateBasicDescription(
        topLabels,
        textDetections,
        response.Brand
      );
    }
  }

  // Determine category from labels
  const category = determineCategory(topLabels);

  // Determine condition from hints or labels
  const condition = response.ConditionHint || determineCondition(topLabels);

  return {
    itemName,
    description,
    category,
    condition,
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

/**
 * Auto-generates a concise, marketable product name based on detected labels
 */
export async function generateItemName(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string,
  config: BedrockConfig = {}
): Promise<string> {
  // Filter high-confidence labels
  const topLabels = labels
    .filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 3);

  // Extract meaningful text
  const meaningfulText = textDetections
    .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
    .map((text) => text.DetectedText)
    .slice(0, 2);

  // Create context for Bedrock
  const context: ItemGenerationContext = {
    labels: topLabels,
    textDetections: meaningfulText,
    brand,
  };

  return bedrockGenerateItemName(context, config);
}

/**
 * Auto-generates an initial description using detected labels and text
 */
export async function generateInitialDescription(
  labels: RekognitionLabel[],
  textDetections: RekognitionTextDetection[],
  brand?: string,
  config: BedrockConfig = {}
): Promise<string> {
  const topLabels = labels
    .filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 5);

  const textInfo = textDetections
    .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
    .map((text) => text.DetectedText)
    .slice(0, 3);

  // Create context for Bedrock
  const context: ItemGenerationContext = {
    labels: topLabels,
    textDetections: textInfo,
    brand,
  };

  return bedrockGenerateInitialDescription(context, config);
}

/**
 * Refines and enhances a description using AWS Bedrock
 */
export async function refineDescriptionWithBedrock(
  initialDescription: string,
  _itemName: string,
  category: string,
  condition: string,
  config: BedrockConfig = {}
): Promise<string> {
  // Create context for Bedrock
  const context: ItemGenerationContext = {
    labels: [], // Not needed for refinement
    textDetections: [], // Not needed for refinement
    category,
    condition,
  };

  return bedrockRefineDescription(initialDescription, context, config);
}

/**
 * Complete AI-powered content generation workflow
 */
export async function generateAIContent(
  rekognitionResponse: RekognitionResponse,
  config: BedrockConfig = {}
): Promise<AIGeneratedContent> {
  try {
    // Generate item name
    const itemName = await generateItemName(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand,
      config
    );

    // Generate initial description
    const initialDescription = await generateInitialDescription(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand,
      config
    );

    // Determine category and condition for refinement
    const topLabels = rekognitionResponse.Labels.filter(
      (label) => label.Confidence > 80
    ).map((label) => label.Name);

    const category = determineCategory(topLabels);
    const condition =
      rekognitionResponse.ConditionHint || determineCondition(topLabels);

    // Refine description with Bedrock
    const refinedDescription = await refineDescriptionWithBedrock(
      initialDescription,
      itemName,
      category,
      condition,
      config
    );

    // Determine confidence based on available data
    const confidence = determineGenerationConfidence(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand
    );

    return {
      itemName,
      initialDescription,
      refinedDescription,
      generationMethod: 'ai',
      confidence,
    };
  } catch (error) {
    console.error('AI content generation error:', error);

    // Fallback to basic generation
    const fallbackName =
      rekognitionResponse.Labels.length > 0
        ? rekognitionResponse.Labels[0].Name
        : 'Item';

    const fallbackDescription =
      'Item details could not be automatically generated. Please provide a detailed description manually.';

    return {
      itemName: fallbackName,
      initialDescription: fallbackDescription,
      refinedDescription: fallbackDescription,
      generationMethod: 'error',
      confidence: 'low',
    };
  }
}

// ===== EXPORTS =====

export default {
  analyzeImageWithRekognition,
  processRekognitionResponse,
  generateItemName,
  generateInitialDescription,
  refineDescriptionWithBedrock,
  generateAIContent,
};
