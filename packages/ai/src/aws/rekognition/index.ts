/**
 * AWS Rekognition module index
 *
 * This file provides a clean API for the rekognition module,
 * and ensures proper imports/exports between the split files.
 */

// Re-export all types
export * from './types';

// Export the main rekognition module
import rekognition from './rekognition';
export default rekognition;

// Export core functions
export {
  analyzeImageWithRekognition,
  generateAIContent,
  generateInitialDescription,
  generateItemName,
  processRekognitionResponse,
  refineDescriptionWithBedrock,
} from './rekognition';

// Export helpers
export {
  checkContentModeration,
  checkFaceDetection,
  determineCategory,
  determineCondition,
  determineGenerationConfidence,
  extractBrandFromLabels,
  extractConditionFromLabels,
} from './helpers';

// Export utility functions
export { fileToBytes, generateBasicDescription } from './utils';

// Export Bedrock integration
export {
  bedrockGenerateInitialDescription,
  bedrockGenerateItemName,
  bedrockRefineDescription,
} from './bedrock';

// Export mock data for testing
export { MOCK_RESPONSES } from './mock-data';
