/**
 * Tests for the refactored AI service
 */

import { AIService } from './ai-service';
import type { RekognitionResponse } from '../types';

// Mock data for testing
const mockRekognitionResponse: RekognitionResponse = {
  Labels: [
    { Name: 'Laptop', Confidence: 95.5 },
    { Name: 'Computer', Confidence: 92.3 },
    { Name: 'Electronics', Confidence: 89.7 },
  ],
  ModerationLabels: [],
  TextDetections: [
    { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
    { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
  ],
  FaceDetails: [],
  Brand: 'Apple',
  ConditionHint: 'Good',
};

// Mock File for testing
const createMockFile = (name: string = 'test.jpg'): File => {
  const blob = new Blob(['mock image data'], { type: 'image/jpeg' });
  return new File([blob], name, { type: 'image/jpeg' });
};

describe('AIService', () => {
  let aiService: AIService;

  beforeEach(() => {
    aiService = new AIService();
  });

  describe('generateContent', () => {
    it('should generate AI content from Rekognition response', async () => {
      const content = await aiService.generateContent(mockRekognitionResponse);

      expect(content).toBeTruthy();
      expect(content.itemName).toBeTruthy();
      expect(content.initialDescription).toBeTruthy();
      expect(content.refinedDescription).toBeTruthy();
      expect(content.generationMethod).toBe('ai');
      expect(['high', 'medium', 'low']).toContain(content.confidence);
    });

    it('should handle empty Rekognition response', async () => {
      const emptyResponse: RekognitionResponse = {
        Labels: [],
        ModerationLabels: [],
        TextDetections: [],
        FaceDetails: [],
      };

      const content = await aiService.generateContent(emptyResponse);

      expect(content.itemName).toBeTruthy();
      expect(content.initialDescription).toBeTruthy();
      expect(content.confidence).toBe('low');
    });
  });

  describe('generateItemName', () => {
    it('should generate item name with brand and text', async () => {
      const itemName = await aiService.generateItemName(
        ['Laptop', 'Computer'],
        ['MacBook Pro', 'Model: A2338'],
        'Apple'
      );

      expect(itemName).toBeTruthy();
      expect(typeof itemName).toBe('string');
      expect(itemName.length).toBeGreaterThan(0);
    });

    it('should handle missing data gracefully', async () => {
      const itemName = await aiService.generateItemName([], [], undefined);

      expect(itemName).toBe('Unidentified Item');
    });
  });

  describe('generateDescription', () => {
    it('should generate description with all data', async () => {
      const description = await aiService.generateDescription(
        ['Laptop', 'Computer'],
        ['MacBook Pro', 'Model: A2338'],
        'Apple'
      );

      expect(description).toBeTruthy();
      expect(typeof description).toBe('string');
      expect(description.length).toBeGreaterThan(50);
    });

    it('should handle missing data gracefully', async () => {
      const description = await aiService.generateDescription(
        [],
        [],
        undefined
      );

      expect(description).toBeTruthy();
      expect(description).toContain('could not be determined');
    });
  });

  describe('refineDescription', () => {
    it('should refine description with category and condition', async () => {
      const initialDescription =
        'This laptop by Apple features computer characteristics.';

      const refinedDescription = await aiService.refineDescription(
        initialDescription,
        'Apple MacBook Pro',
        'Electronics',
        'Good'
      );

      expect(refinedDescription).toBeTruthy();
      expect(typeof refinedDescription).toBe('string');
      expect(refinedDescription.length).toBeGreaterThan(
        initialDescription.length
      );
    });
  });

  describe('error handling', () => {
    it('should handle service errors gracefully', async () => {
      // Test with invalid input that might cause errors
      try {
        await aiService.generateItemName([''], [''], '');
        // Should not throw, should handle gracefully
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toContain('generation failed');
      }
    });
  });
});

describe('AI Service Integration', () => {
  it('should have consistent API across all functions', () => {
    const aiService = new AIService();

    // Check that all expected methods exist
    expect(typeof aiService.analyzeImage).toBe('function');
    expect(typeof aiService.generateContent).toBe('function');
    expect(typeof aiService.generateItemName).toBe('function');
    expect(typeof aiService.generateDescription).toBe('function');
    expect(typeof aiService.refineDescription).toBe('function');
    expect(typeof aiService.analyzeAndGenerate).toBe('function');
    expect(typeof aiService.testConfiguration).toBe('function');
  });
});
