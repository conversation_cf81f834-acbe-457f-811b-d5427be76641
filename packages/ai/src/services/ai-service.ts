/**
 * Main AI service that orchestrates Rekognition and Bedrock
 */

import { processRekognitionResponse } from '../analysis/processors';
import { getBedrockService } from '../aws/bedrock/service';
import { getRekognitionService } from '../aws/rekognition/service';
import {
  generateAIContent,
  generateInitialDescription,
  generateItemName,
  refineDescription,
} from '../content/generators';
import type {
  AIGeneratedContent,
  BedrockService,
  ProcessedAIResult,
  RekognitionResponse,
  RekognitionService,
} from '../types';

/**
 * Main AI service class
 */
export class AIService {
  private rekognitionService: RekognitionService;
  private bedrockService: BedrockService;

  constructor() {
    this.rekognitionService = getRekognitionService();
    this.bedrockService = getBedrockService();
  }

  /**
   * Analyzes an image and returns processed AI results
   */
  async analyzeImage(imageFile: File): Promise<ProcessedAIResult> {
    try {
      // Analyze image with Rekognition
      const rekognitionResponse = await this.rekognitionService.analyse(
        imageFile
      );

      // Process the response
      const processedResult = await processRekognitionResponse(
        rekognitionResponse
      );

      return processedResult;
    } catch (error) {
      console.error('Image analysis failed:', error);
      throw new Error(
        `Image analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Generates AI-powered content from Rekognition response
   */
  async generateContent(
    rekognitionResponse: RekognitionResponse
  ): Promise<AIGeneratedContent> {
    try {
      return await generateAIContent(rekognitionResponse);
    } catch (error) {
      console.error('Content generation failed:', error);
      throw new Error(
        `Content generation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Generates just an item name
   */
  async generateItemName(
    labels: string[],
    textDetections: string[],
    brand?: string
  ): Promise<string> {
    try {
      return await generateItemName(labels, textDetections, brand);
    } catch (error) {
      console.error('Item name generation failed:', error);
      throw new Error(
        `Item name generation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Generates an initial description
   */
  async generateDescription(
    labels: string[],
    textDetections: string[],
    brand?: string
  ): Promise<string> {
    try {
      return await generateInitialDescription(labels, textDetections, brand);
    } catch (error) {
      console.error('Description generation failed:', error);
      throw new Error(
        `Description generation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Refines an existing description
   */
  async refineDescription(
    initialDescription: string,
    itemName: string,
    category: string,
    condition: string
  ): Promise<string> {
    try {
      return await refineDescription(
        initialDescription,
        itemName,
        category,
        condition
      );
    } catch (error) {
      console.error('Description refinement failed:', error);
      throw new Error(
        `Description refinement failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Complete workflow: analyze image and generate content
   */
  async analyzeAndGenerate(imageFile: File): Promise<{
    analysis: ProcessedAIResult;
    content: AIGeneratedContent;
  }> {
    try {
      // Analyze image
      const rekognitionResponse = await this.rekognitionService.analyse(
        imageFile
      );

      // Process analysis
      const analysis = await processRekognitionResponse(rekognitionResponse);

      // Generate content
      const content = await generateAIContent(rekognitionResponse);

      return { analysis, content };
    } catch (error) {
      console.error('Complete AI workflow failed:', error);
      throw new Error(
        `AI workflow failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Test configuration and connectivity
   */
  async testConfiguration(): Promise<{
    rekognition: boolean;
    bedrock: boolean;
    error?: string;
  }> {
    const result = {
      rekognition: false,
      bedrock: false,
      error: undefined as string | undefined,
    };

    try {
      // Test Rekognition with a minimal request
      // Note: This would require a test image, so we'll just check if the service initializes
      const rekognitionService = getRekognitionService();
      result.rekognition = !!rekognitionService;
    } catch (error) {
      result.error = `Rekognition test failed: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`;
    }

    try {
      // Test Bedrock with a simple prompt
      const bedrockService = getBedrockService();
      const testResponse = await bedrockService.send(
        'Test prompt: respond with "OK"'
      );
      result.bedrock =
        testResponse.content.includes('OK') || testResponse.content.length > 0;
    } catch (error) {
      result.error = result.error
        ? `${result.error}; Bedrock test failed: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        : `Bedrock test failed: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`;
    }

    return result;
  }
}

/**
 * Singleton instance of AI service
 */
let aiServiceInstance: AIService | null = null;

/**
 * Get the AI service instance
 */
export function getAIService(): AIService {
  if (!aiServiceInstance) {
    aiServiceInstance = new AIService();
  }
  return aiServiceInstance;
}

/**
 * Convenience functions that use the singleton service
 */

export async function analyzeImage(
  imageFile: File
): Promise<ProcessedAIResult> {
  return getAIService().analyzeImage(imageFile);
}

export async function generateAIContentFromImage(
  imageFile: File
): Promise<AIGeneratedContent> {
  const rekognitionResponse = await getRekognitionService().analyse(imageFile);
  return getAIService().generateContent(rekognitionResponse);
}

export async function analyzeAndGenerateContent(imageFile: File): Promise<{
  analysis: ProcessedAIResult;
  content: AIGeneratedContent;
}> {
  return getAIService().analyzeAndGenerate(imageFile);
}
