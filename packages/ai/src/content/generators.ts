/**
 * Content generation functions using AI services
 */

import { getBedrockService } from '../aws/bedrock/service';
import { createItemNamePrompt, createInitialDescriptionPrompt, createRefinementPrompt } from '../prompts';
import type { 
  ItemGenerationContext, 
  AIGeneratedContent, 
  RekognitionResponse,
  AnalysisConfidence 
} from '../types';

/**
 * Generates a concise, marketable product name
 */
export async function generateItemName(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  // Check for edge cases
  if (labels.length === 0 && textDetections.length === 0) {
    return 'Unidentified Item';
  }

  const context: ItemGenerationContext = {
    labels,
    textDetections,
    brand,
  };

  try {
    const bedrockService = getBedrockService();
    const prompt = createItemNamePrompt(context);
    const response = await bedrockService.send(prompt);
    return response.content.trim();
  } catch (error) {
    console.error('Item name generation failed:', error);
    
    // Fallback logic
    if (textDetections.length > 0) {
      const text = textDetections[0];
      if (brand && !text.toLowerCase().includes(brand.toLowerCase())) {
        return `${brand} ${text}`;
      }
      return text;
    }

    if (labels.length > 0) {
      const mainItem = labels[0];
      if (brand) {
        return `${brand} ${mainItem}`;
      }
      return mainItem;
    }

    return 'Unidentified Item';
  }
}

/**
 * Generates an initial product description
 */
export async function generateInitialDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  if (labels.length === 0) {
    return 'Item details could not be determined from the image. Please provide a detailed description manually.';
  }

  const context: ItemGenerationContext = {
    labels,
    textDetections,
    brand,
  };

  try {
    const bedrockService = getBedrockService();
    const prompt = createInitialDescriptionPrompt(context);
    const response = await bedrockService.send(prompt);
    return response.content.trim();
  } catch (error) {
    console.error('Initial description generation failed:', error);
    
    // Fallback logic
    const mainItem = labels[0];
    let description = `This ${mainItem.toLowerCase()}`;

    if (brand) {
      description += ` by ${brand}`;
    }

    if (textDetections.length > 0) {
      const modelInfo = textDetections.join(', ');
      description += ` (${modelInfo})`;
    }

    const additionalLabels = labels.slice(1, 3);
    if (additionalLabels.length > 0) {
      description += ` features ${additionalLabels
        .map((label) => label.toLowerCase())
        .join(' and ')} characteristics`;
    }

    description += '. Please review the photos and description for full details.';
    
    return description;
  }
}

/**
 * Refines and enhances a description for marketplace appeal
 */
export async function refineDescription(
  initialDescription: string,
  itemName: string,
  category: string,
  condition: string
): Promise<string> {
  const context: ItemGenerationContext = {
    labels: [], // Not needed for refinement
    textDetections: [], // Not needed for refinement
    category,
    condition,
  };

  try {
    const bedrockService = getBedrockService();
    const prompt = createRefinementPrompt(initialDescription, context);
    const response = await bedrockService.send(prompt);
    return response.content.trim();
  } catch (error) {
    console.error('Description refinement failed:', error);
    
    // Fallback: return original with basic enhancement
    let refined = initialDescription;
    
    // Add basic category enhancement
    const categoryEnhancements: Record<string, string> = {
      Electronics: 'Perfect for tech enthusiasts seeking reliable, high-quality electronics.',
      Furniture: 'An excellent addition to any home or office, combining style and functionality.',
      Clothing: 'Stylish and comfortable, ideal for fashion-conscious buyers.',
      Books: 'A valuable addition to any library or collection.',
      Sports: 'Essential gear for fitness enthusiasts and active lifestyles.',
      Toys: 'Great for children and collectors who appreciate quality items.',
      Home: 'Perfect for organizing and enhancing your living space.',
      Automotive: 'Quality automotive equipment for vehicle enthusiasts.',
    };

    if (categoryEnhancements[category]) {
      refined += ` ${categoryEnhancements[category]}`;
    }

    refined += ' Please review all photos and contact the seller for additional details.';
    
    return refined;
  }
}

/**
 * Complete AI-powered content generation workflow
 */
export async function generateAIContent(
  rekognitionResponse: RekognitionResponse
): Promise<AIGeneratedContent> {
  try {
    // Filter high-confidence labels
    const topLabels = rekognitionResponse.Labels
      .filter((label) => label.Confidence > 80)
      .map((label) => label.Name)
      .slice(0, 5);

    const meaningfulText = rekognitionResponse.TextDetections
      .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
      .map((text) => text.DetectedText)
      .slice(0, 3);

    // Generate item name
    const itemName = await generateItemName(
      topLabels,
      meaningfulText,
      rekognitionResponse.Brand
    );

    // Generate initial description
    const initialDescription = await generateInitialDescription(
      topLabels,
      meaningfulText,
      rekognitionResponse.Brand
    );

    // Determine category and condition for refinement
    const category = determineCategory(topLabels);
    const condition = rekognitionResponse.ConditionHint || 'Good';

    // Refine description
    const refinedDescription = await refineDescription(
      initialDescription,
      itemName,
      category,
      condition
    );

    // Determine confidence based on available data
    const confidence = determineGenerationConfidence(
      rekognitionResponse.Labels,
      rekognitionResponse.TextDetections,
      rekognitionResponse.Brand
    );

    return {
      itemName,
      initialDescription,
      refinedDescription,
      generationMethod: 'ai',
      confidence,
    };
  } catch (error) {
    console.error('AI content generation error:', error);
    
    // Fallback to basic generation
    const fallbackName = rekognitionResponse.Labels.length > 0 
      ? rekognitionResponse.Labels[0].Name 
      : 'Item';
    
    const fallbackDescription = 'Item details could not be automatically generated. Please provide a detailed description manually.';

    return {
      itemName: fallbackName,
      initialDescription: fallbackDescription,
      refinedDescription: fallbackDescription,
      generationMethod: 'error',
      confidence: 'low',
    };
  }
}

/**
 * Determines confidence level for AI generation
 */
function determineGenerationConfidence(
  labels: any[],
  textDetections: any[],
  brand?: string
): AnalysisConfidence {
  const highConfidenceLabels = labels.filter((label) => label.Confidence > 90);
  const highConfidenceText = textDetections.filter((text) => text.Confidence > 90);

  if (highConfidenceLabels.length >= 3 && (highConfidenceText.length > 0 || brand)) {
    return 'high';
  }

  if (highConfidenceLabels.length >= 2 || highConfidenceText.length > 0) {
    return 'medium';
  }

  return 'low';
}

/**
 * Determines category from labels
 */
function determineCategory(labels: string[]): string {
  const categoryMap: Record<string, string[]> = {
    Electronics: ['laptop', 'computer', 'phone', 'tablet', 'camera', 'electronics', 'device'],
    Furniture: ['chair', 'table', 'desk', 'sofa', 'bed', 'furniture', 'cabinet'],
    Clothing: ['shirt', 'pants', 'dress', 'shoes', 'clothing', 'apparel', 'jacket'],
    Books: ['book', 'magazine', 'journal', 'publication', 'text'],
    Sports: ['ball', 'equipment', 'sports', 'fitness', 'exercise', 'athletic'],
    Toys: ['toy', 'game', 'doll', 'puzzle', 'plaything'],
    Home: ['kitchen', 'bathroom', 'home', 'household', 'appliance'],
    Automotive: ['car', 'vehicle', 'automotive', 'auto', 'motor'],
  };

  for (const [category, keywords] of Object.entries(categoryMap)) {
    if (labels.some(label => 
      keywords.some(keyword => 
        label.toLowerCase().includes(keyword)
      )
    )) {
      return category;
    }
  }

  return 'Other';
}
