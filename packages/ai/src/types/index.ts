/**
 * Core types for AI package
 */

// ===== REKOGNITION TYPES =====

export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Confidence: number;
  Type: 'LINE' | 'WORD';
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionFaceDetail {
  Confidence: number;
}

export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

// ===== BEDROCK TYPES =====

export interface BedrockResponse {
  content: string;
  usage: {
    inputTokens: number;
    outputTokens: number;
  };
}

export interface ItemGenerationContext {
  labels: string[];
  textDetections: string[];
  brand?: string;
  category?: string;
  condition?: string;
}

// ===== AI CONTENT TYPES =====

export interface AIGeneratedContent {
  itemName: string;
  initialDescription: string;
  refinedDescription: string;
  generationMethod: 'ai' | 'fallback' | 'error';
  confidence: 'high' | 'medium' | 'low';
}

export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

// ===== SERVICE INTERFACES =====

export interface RekognitionService {
  analyse: (imageFile: File) => Promise<RekognitionResponse>;
  detectLabels: (imageBytes: Uint8Array) => Promise<any>;
  detectText: (imageBytes: Uint8Array) => Promise<any>;
  detectModerationLabels: (imageBytes: Uint8Array) => Promise<any>;
  detectFaces: (imageBytes: Uint8Array) => Promise<any>;
}

export interface BedrockService {
  send: (prompt: string) => Promise<BedrockResponse>;
}

// ===== ANALYSIS TYPES =====

export type AnalysisConfidence = 'high' | 'medium' | 'low';
export type GenerationMethod = 'ai' | 'fallback' | 'error';
export type ItemCondition = 'Like New' | 'Good' | 'Fair' | 'Poor';
export type ItemCategory = 
  | 'Electronics' 
  | 'Furniture' 
  | 'Clothing' 
  | 'Books' 
  | 'Sports' 
  | 'Toys' 
  | 'Home' 
  | 'Automotive'
  | 'Other';
