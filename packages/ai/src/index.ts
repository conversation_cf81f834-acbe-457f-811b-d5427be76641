// ===== MAIN AI SERVICE =====
export {
  AIService,
  analyzeAndGenerateContent,
  analyzeImage,
  generateAIContentFromImage,
  getAIService,
} from './services/ai-service';

// ===== TYPES =====
export * from './types';

// ===== CONTENT GENERATORS =====
export {
  generateAIContent,
  generateInitialDescription,
  generateItemName,
  refineDescription,
} from './content/generators';

// ===== ANALYSIS PROCESSORS =====
export { processRekognitionResponse } from './analysis/processors';

// ===== AWS SERVICES =====
export { getRekognitionService } from './aws/rekognition/service';

export { getBedrockService } from './aws/bedrock/service';

// ===== PROMPTS =====
export {
  createInitialDescriptionPrompt,
  createItemNamePrompt,
  createRefinementPrompt,
} from './prompts';
