/**
 * Analysis processors for AI results
 */

import { generateAIContent } from '../content/generators';
import type { 
  RekognitionResponse, 
  ProcessedAIResult,
  RekognitionLabel,
  RekognitionTextDetection 
} from '../types';

/**
 * Processes raw Rekognition response into structured AI result
 */
export async function processRekognitionResponse(
  response: RekognitionResponse
): Promise<ProcessedAIResult> {
  // Filter high-confidence labels
  const topLabels = response.Labels
    .filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 10);

  // Extract meaningful text
  const textDetections = response.TextDetections
    .filter((text) => text.Confidence > 85 && text.Type === 'LINE')
    .map((text) => text.DetectedText)
    .slice(0, 5);

  // Check for special cases
  const isZeroAnalysis = topLabels.length === 0 && textDetections.length === 0;
  const isUnrecognized = topLabels.length > 0 && 
    topLabels.every(label => 
      ['object', 'thing', 'item', 'unidentified'].some(generic => 
        label.toLowerCase().includes(generic)
      )
    );

  // Generate AI-powered item name and description
  let itemName = '';
  let description = '';

  if (isZeroAnalysis) {
    itemName = 'No Data Detected';
    description = 'No analyzable data was detected in this image. This could be due to poor lighting, blur, or an empty/unclear photo. Please try uploading a clearer image or fill in the details manually.';
  } else if (isUnrecognized) {
    itemName = 'Unrecognized Item';
    description = 'AI could not clearly identify this item from the image. Please manually enter the item details below for the best listing results.';
  } else {
    try {
      const aiContent = await generateAIContent(response);
      itemName = aiContent.itemName;
      description = aiContent.refinedDescription;
    } catch (error) {
      console.error('AI content generation failed, falling back to basic generation:', error);
      
      // Fallback to basic generation
      if (textDetections.length > 0) {
        itemName = textDetections[0];
      } else if (topLabels.length > 0) {
        itemName = topLabels[0];
      } else {
        itemName = 'Item';
      }

      description = generateBasicDescription(topLabels, textDetections, response.Brand);
    }
  }

  // Determine category
  const category = determineCategory(topLabels);

  // Determine condition
  const condition = response.ConditionHint || determineCondition(topLabels);

  // Check for content warnings
  const contentWarning = checkContentWarnings(response.ModerationLabels);
  const faceWarning = checkFaceWarnings(response.FaceDetails);

  return {
    itemName,
    description,
    category,
    condition,
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

/**
 * Generates a basic description as fallback
 */
function generateBasicDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): string {
  if (labels.length === 0) {
    return 'Item details could not be determined from the image. Please provide a detailed description manually.';
  }

  const mainItem = labels[0];
  let description = `This ${mainItem.toLowerCase()}`;

  if (brand) {
    description += ` by ${brand}`;
  }

  if (textDetections.length > 0) {
    const modelInfo = textDetections.join(', ');
    description += ` (${modelInfo})`;
  }

  const additionalLabels = labels.slice(1, 3);
  if (additionalLabels.length > 0) {
    description += ` features ${additionalLabels
      .map((label) => label.toLowerCase())
      .join(' and ')} characteristics`;
  }

  description += '. Please review the photos and description for full details.';
  
  return description;
}

/**
 * Determines category from labels
 */
function determineCategory(labels: string[]): string {
  const categoryMap: Record<string, string[]> = {
    Electronics: ['laptop', 'computer', 'phone', 'tablet', 'camera', 'electronics', 'device', 'monitor', 'keyboard', 'mouse'],
    Furniture: ['chair', 'table', 'desk', 'sofa', 'bed', 'furniture', 'cabinet', 'shelf', 'dresser', 'couch'],
    Clothing: ['shirt', 'pants', 'dress', 'shoes', 'clothing', 'apparel', 'jacket', 'coat', 'hat', 'accessory'],
    Books: ['book', 'magazine', 'journal', 'publication', 'text', 'novel', 'manual', 'guide'],
    Sports: ['ball', 'equipment', 'sports', 'fitness', 'exercise', 'athletic', 'gym', 'workout', 'bicycle', 'skateboard'],
    Toys: ['toy', 'game', 'doll', 'puzzle', 'plaything', 'action figure', 'board game', 'video game'],
    Home: ['kitchen', 'bathroom', 'home', 'household', 'appliance', 'cookware', 'utensil', 'decoration', 'lamp'],
    Automotive: ['car', 'vehicle', 'automotive', 'auto', 'motor', 'tire', 'engine', 'part', 'accessory'],
  };

  for (const [category, keywords] of Object.entries(categoryMap)) {
    if (labels.some(label => 
      keywords.some(keyword => 
        label.toLowerCase().includes(keyword)
      )
    )) {
      return category;
    }
  }

  return 'Other';
}

/**
 * Determines condition from labels (basic heuristic)
 */
function determineCondition(labels: string[]): string {
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect', 'unused'],
    'Good': ['good', 'clean', 'well-maintained', 'functional'],
    'Fair': ['used', 'worn', 'fair', 'some wear'],
    'Poor': ['damaged', 'broken', 'poor', 'needs repair', 'worn out'],
  };

  for (const [condition, keywords] of Object.entries(conditionKeywords)) {
    if (labels.some(label => 
      keywords.some(keyword => 
        label.toLowerCase().includes(keyword)
      )
    )) {
      return condition;
    }
  }

  return 'Good'; // Default condition
}

/**
 * Checks for content warnings from moderation labels
 */
function checkContentWarnings(moderationLabels: any[]): string | undefined {
  if (moderationLabels.length === 0) return undefined;

  const highConfidenceLabels = moderationLabels
    .filter((label) => label.Confidence > 75)
    .map((label) => label.Name);

  if (highConfidenceLabels.length === 0) return undefined;

  const warningTypes = {
    'Explicit Nudity': ['Explicit Nudity', 'Nudity'],
    'Violence': ['Violence', 'Graphic Violence', 'Weapons'],
    'Drugs': ['Drugs', 'Drug Use', 'Drug Paraphernalia'],
    'Hate Symbols': ['Hate Symbols', 'Nazi Party'],
    'Suggestive': ['Suggestive', 'Partial Nudity'],
  };

  for (const [warningType, keywords] of Object.entries(warningTypes)) {
    if (highConfidenceLabels.some(label => 
      keywords.some(keyword => label.includes(keyword))
    )) {
      return `Content Warning: This image may contain ${warningType.toLowerCase()}. Please review marketplace policies before listing.`;
    }
  }

  return `Content Warning: This image was flagged for review. Please ensure it complies with marketplace policies.`;
}

/**
 * Checks for face warnings
 */
function checkFaceWarnings(faceDetails: any[]): string | undefined {
  if (faceDetails.length === 0) return undefined;

  const highConfidenceFaces = faceDetails.filter((face) => face.Confidence > 90);

  if (highConfidenceFaces.length === 0) return undefined;

  if (highConfidenceFaces.length === 1) {
    return 'Privacy Notice: A face was detected in this image. Consider blurring faces for privacy protection.';
  }

  return `Privacy Notice: ${highConfidenceFaces.length} faces were detected in this image. Consider blurring faces for privacy protection.`;
}
