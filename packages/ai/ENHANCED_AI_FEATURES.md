# Enhanced AI Features for AWS Rekognition

This document describes the new AI-powered features that have been added to the AWS Rekognition functionality in `packages/ai/src/aws/rekognition/rekognition.ts`.

## New Features

### 1. Auto-generate Item Name
Creates concise, marketable product names based on detected labels from Rekognition.

**Function:** `generateItemName()`
- Uses detected labels and text to create appealing product names
- Includes brand information when available
- Fallback handling for unrecognized items
- Maximum 6 words for marketplace compatibility

### 2. Auto-generate Item Description
Generates initial descriptions using detected labels and text from images.

**Function:** `generateInitialDescription()`
- Creates 2-3 sentence descriptions based on image analysis
- Incorporates brand and model information
- Focuses on key features and characteristics
- Suitable for marketplace listings

### 3. Refine Description with AWS Bedrock
Uses AWS Bedrock to enhance and polish generated descriptions.

**Function:** `refineDescriptionWithBedrock()`
- Enhances descriptions to be more compelling and marketplace-ready
- Adds category-specific selling points
- Includes appropriate condition details
- Maintains professional, trustworthy tone

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# AWS Bedrock Configuration
AMZ_BEDROCK_MODEL_ID=anthropic.claude-3-haiku-20240307-v1:0
USE_MOCK_BEDROCK=true

# Existing AWS Configuration (required)
AMZ_REGION=us-east-1
AMZ_ACCESS_KEY_ID=your_access_key
AMZ_SECRET_ACCESS_KEY=your_secret_key
USE_MOCK_REKOGNITION=true
```

### BedrockConfig Interface

```typescript
interface BedrockConfig {
  useMockData?: boolean;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  sessionToken?: string;
  modelId?: string;
  maxTokens?: number;
  temperature?: number;
}
```

## Usage Examples

### Basic Usage - Complete AI Workflow

```typescript
import { generateAIContent, analyzeImageWithRekognition } from '@package/ai';

// Analyze image and generate all content
const rekognitionResponse = await analyzeImageWithRekognition(imageFile);
const aiContent = await generateAIContent(rekognitionResponse, {
  useMockData: true, // Set to false for production
  modelId: 'anthropic.claude-3-haiku-20240307-v1:0'
});

console.log(aiContent);
// {
//   itemName: "Apple MacBook Pro 13-inch",
//   initialDescription: "This laptop by Apple (MacBook Pro, Model: A2338, 13-inch)...",
//   refinedDescription: "This laptop by Apple (MacBook Pro, Model: A2338, 13-inch)...",
//   generationMethod: "ai",
//   confidence: "high"
// }
```

### Individual Feature Usage

```typescript
import {
  generateItemName,
  generateInitialDescription,
  refineDescriptionWithBedrock
} from '@package/ai';

// Generate just the item name
const itemName = await generateItemName(
  rekognitionResponse.Labels,
  rekognitionResponse.TextDetections,
  rekognitionResponse.Brand
);

// Generate initial description
const initialDesc = await generateInitialDescription(
  rekognitionResponse.Labels,
  rekognitionResponse.TextDetections,
  rekognitionResponse.Brand
);

// Refine existing description
const refinedDesc = await refineDescriptionWithBedrock(
  initialDesc,
  itemName,
  'Electronics',
  'Good'
);
```

### Enhanced processRekognitionResponse

The existing `processRekognitionResponse` function has been enhanced to use the new AI features:

```typescript
import { processRekognitionResponse } from '@package/ai';

const result = await processRekognitionResponse(rekognitionResponse, {
  useMockData: true,
  modelId: 'anthropic.claude-3-haiku-20240307-v1:0'
});
```

## Server Actions

New server actions are available in `enhanced-ai-analysis.action.ts`:

### enhancedAnalyzeImageAction
Complete workflow with Rekognition + Bedrock integration.

### generateItemNameAction
Generate just the item name from an image.

### refineDescriptionAction
Refine an existing description using Bedrock.

### testEnhancedAIConfigAction
Test configuration for both Rekognition and Bedrock.

## Mock vs Real Implementation

### Mock Mode (Development)
- Set `USE_MOCK_BEDROCK=true`
- Uses predefined templates and logic
- No AWS API calls or costs
- Consistent results for testing

### Production Mode
- Set `USE_MOCK_BEDROCK=false`
- Requires valid AWS credentials
- Uses real AWS Bedrock API
- Dynamic, AI-generated content

## Error Handling

All functions include comprehensive error handling:
- Graceful fallbacks to basic generation
- Detailed error logging
- User-friendly error messages
- Maintains functionality even if Bedrock fails

## Integration with Existing Features

The enhanced functionality:
- ✅ Follows existing modular architecture patterns
- ✅ Uses server-side actions as preferred
- ✅ Integrates with existing AWS Rekognition results
- ✅ Maintains same error handling patterns
- ✅ Works with toggle switches and field clearing
- ✅ Suitable for marketplace item listings
- ✅ Includes appropriate TypeScript types

## Dependencies

The following dependency has been added:
- `@aws-sdk/client-bedrock-runtime`: For AWS Bedrock API integration

## Testing

Use the test configuration action to verify setup:

```typescript
const configTest = await testEnhancedAIConfigAction();
console.log(configTest.config);
```

## Next Steps

1. Configure environment variables
2. Test with mock data first
3. Set up AWS Bedrock credentials for production
4. Integrate with existing AI-powered forms
5. Add UI components for the new features
