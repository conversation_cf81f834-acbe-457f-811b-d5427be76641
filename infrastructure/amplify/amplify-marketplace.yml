version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - mkdir -p apps/webapp-marketplace/.next
            - touch .env
            - echo "CLOUD_PROVIDER=AMPLIFY" >> .env
            - echo "AUTH_TRUST_HOST=true" >> .env
            - env | grep -e AUTH_SECRET >> .env
            - env | grep -e AMZ_REGION >> .env
            - env | grep -e AMZ_ACCESS_KEY_ID >> .env
            - env | grep -e AMZ_SECRET_ACCESS_KEY >> .env
            - env | grep -e AMZ_DYNAMODB_TABLE_CORE >> .env
            - env | grep -e AMZ_DYNAMODB_TABLE_AUTH >> .env
            - env | grep -e AMZ_S3_BUCKET >> .env
            - env | grep -e AMZ_S3_BUCKET_REGION >> .env
            - env | grep -e AMZ_S3_ACCESS_KEY_ID >> .env
            - env | grep -e AMZ_S3_SECRET_ACCESS_KEY >> .env
            - env | grep -e AMZ_DYNAMODB_TABLE_ACCOUNT >> .env
            - env | grep -e SENDGRID_API_KEY >> .env
            - env | grep -e SENDGRID_FROM_EMAIL >> .env
            - env | grep -e SENDGRID_FROM_NAME >> .env
            - env | grep -e NEXT_PUBLIC_BASE_URL >> .env
            - env | grep -e AUTH_URL >> .env
            - cp .env apps/webapp-marketplace/
            - npm ci --cache .npm --prefer-offline
        build:
          commands:
            - npx nx build webapp-marketplace --verbose
      artifacts:
        baseDirectory: apps/webapp-marketplace/.next
        files:
          - '**/*'
      cache:
        paths:
          - .next/cache/**/*
          - .npm/**/*
      buildPath: /
    appRoot: apps/webapp-marketplace
